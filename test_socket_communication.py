#!/usr/bin/env python3
"""
Socket通信测试脚本
用于验证.NET后端与Python后端之间的通信是否正常
"""

import socket
import json
import threading
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('socket_test.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SocketTestServer:
    def __init__(self, host='*************', port=5007):
        self.host = host
        self.port = port
        self.server_socket = None
        self.client_connections = []
        self.running = False
        
    def start_server(self):
        """启动测试服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            
            logger.info(f"测试服务器启动在 {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    logger.info(f"新客户端连接: {address}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                    self.client_connections.append(client_socket)
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"接受连接时出错: {e}")
                        
        except Exception as e:
            logger.error(f"启动服务器失败: {e}")
        finally:
            self.cleanup()
    
    def handle_client(self, client_socket, address):
        """处理客户端连接"""
        buffer = b""
        
        try:
            while self.running:
                try:
                    data = client_socket.recv(4096)
                    if not data:
                        logger.info(f"客户端 {address} 断开连接")
                        break
                    
                    buffer += data
                    
                    # 尝试解析JSON消息
                    while buffer:
                        try:
                            # 尝试解析JSON
                            message_str = buffer.decode('utf-8')
                            message = json.loads(message_str)
                            
                            logger.info(f"收到来自 {address} 的消息: {message}")
                            
                            # 处理消息
                            response = self.process_message(message, address)
                            if response:
                                response_json = json.dumps(response)
                                client_socket.send(response_json.encode('utf-8'))
                                logger.info(f"发送响应给 {address}: {response}")
                            
                            buffer = b""  # 清空缓冲区
                            break
                            
                        except json.JSONDecodeError:
                            # JSON不完整，等待更多数据
                            break
                        except UnicodeDecodeError:
                            # 编码错误，清空缓冲区
                            logger.error(f"解码错误，清空缓冲区: {buffer[:100]}")
                            buffer = b""
                            break
                            
                except socket.timeout:
                    continue
                except socket.error as e:
                    logger.error(f"处理客户端 {address} 时出错: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"处理客户端 {address} 时发生异常: {e}")
        finally:
            try:
                client_socket.close()
                if client_socket in self.client_connections:
                    self.client_connections.remove(client_socket)
            except:
                pass
    
    def process_message(self, message, address):
        """处理接收到的消息"""
        message_type = message.get('type', '')
        content = message.get('content', '')
        
        logger.info(f"处理消息类型: {message_type}, 内容: {content}")
        
        if message_type == 'start_all':
            # 模拟启动所有节点的响应
            return {
                "type": "start_success",
                "content": {
                    "nodes_name": ["drone_1", "drone_2", "drone_3"],
                    "x": [100.0, 200.0, 300.0],
                    "y": [150.0, 250.0, 350.0],
                    "cpu_used_rate": [25.5, 30.2, 15.8],
                    "left_bandwidth": [80.5, 75.3, 90.1],
                    "memory": [512.0, 1024.0, 768.0],
                    "radius": [50.0, 50.0, 50.0],
                    "deal_speed": [10.5, 12.3, 8.7]
                }
            }
        
        elif message_type == 'node_info':
            # 模拟节点信息响应
            return {
                "type": "ans_node_info",
                "content": {
                    "nodes_name": ["drone_1", "drone_2", "drone_3"],
                    "x": [110.0, 210.0, 310.0],
                    "y": [160.0, 260.0, 360.0],
                    "cpu_used_rate": [28.5, 32.2, 18.8],
                    "left_bandwidth": [78.5, 73.3, 88.1],
                    "memory": [512.0, 1024.0, 768.0],
                    "radius": [50.0, 50.0, 50.0],
                    "deal_speed": [10.5, 12.3, 8.7]
                }
            }
        
        elif message_type == 'shutdown':
            # 模拟关闭节点
            drone_name = content
            logger.info(f"收到关闭指令，目标无人机: {drone_name}")
            
            # 这里可以添加实际的关闭逻辑
            return {
                "type": "shutdown_ack",
                "content": f"无人机 {drone_name} 已成功关闭"
            }
        
        elif message_type == 'create_tasks':
            # 模拟创建任务
            task_description = content
            logger.info(f"收到创建任务指令: {task_description}")
            
            return {
                "type": "task_created",
                "content": f"任务已创建: {task_description}"
            }
        
        else:
            logger.warning(f"未知消息类型: {message_type}")
            return {
                "type": "error",
                "content": f"未知消息类型: {message_type}"
            }
    
    def stop_server(self):
        """停止服务器"""
        logger.info("正在停止测试服务器...")
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.client_connections:
            try:
                client.close()
            except:
                pass
        
        self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        logger.info("测试服务器已停止")

def main():
    """主函数"""
    server = SocketTestServer()
    
    try:
        # 启动服务器
        server_thread = threading.Thread(target=server.start_server)
        server_thread.daemon = True
        server_thread.start()
        
        logger.info("测试服务器已启动，按 Ctrl+C 停止")
        
        # 保持主线程运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        server.stop_server()

if __name__ == "__main__":
    main()
