namespace WebApplication_Drone.Services.Interfaces
{
    /// <summary>
    /// 缓存服务接口
    /// </summary>
    public interface ICacheService
    {
        #region 基础缓存操作
        /// <summary>
        /// 设置缓存
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>操作结果</returns>
        Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null);

        /// <summary>
        /// 获取缓存
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值</returns>
        Task<T?> GetAsync<T>(string key);

        /// <summary>
        /// 删除缓存
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>操作结果</returns>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// 检查缓存是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// 设置缓存过期时间
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>操作结果</returns>
        Task<bool> SetExpiryAsync(string key, TimeSpan expiry);
        #endregion

        #region 批量操作
        /// <summary>
        /// 批量设置缓存
        /// </summary>
        /// <param name="items">缓存项</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>操作结果</returns>
        Task<bool> SetBatchAsync<T>(Dictionary<string, T> items, TimeSpan? expiry = null);

        /// <summary>
        /// 批量获取缓存
        /// </summary>
        /// <param name="keys">缓存键列表</param>
        /// <returns>缓存值字典</returns>
        Task<Dictionary<string, T?>> GetBatchAsync<T>(IEnumerable<string> keys);

        /// <summary>
        /// 批量删除缓存
        /// </summary>
        /// <param name="keys">缓存键列表</param>
        /// <returns>操作结果</returns>
        Task<bool> RemoveBatchAsync(IEnumerable<string> keys);
        #endregion

        #region 模式操作
        /// <summary>
        /// 按模式删除缓存
        /// </summary>
        /// <param name="pattern">模式</param>
        /// <returns>删除的数量</returns>
        Task<long> RemoveByPatternAsync(string pattern);

        /// <summary>
        /// 按模式获取所有键
        /// </summary>
        /// <param name="pattern">模式</param>
        /// <returns>键列表</returns>
        Task<IEnumerable<string>> GetKeysByPatternAsync(string pattern);

        /// <summary>
        /// 获取缓存信息
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>缓存信息</returns>
        Task<CacheInfo?> GetCacheInfoAsync(string key);
        #endregion

        #region 统计和监控
        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<CacheStatistics> GetStatisticsAsync();

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        /// <returns>操作结果</returns>
        Task<bool> FlushAllAsync();

        /// <summary>
        /// 获取缓存大小
        /// </summary>
        /// <returns>缓存大小</returns>
        Task<long> GetCacheSizeAsync();

        /// <summary>
        /// 检查缓存服务健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        Task<bool> IsHealthyAsync();
        #endregion
    }

    /// <summary>
    /// 缓存信息
    /// </summary>
    public class CacheInfo
    {
        public string Key { get; set; } = string.Empty;
        public DateTime? ExpiryTime { get; set; }
        public TimeSpan? TimeToLive { get; set; }
        public bool Exists { get; set; }
        public string Type { get; set; } = string.Empty;
        public long Size { get; set; }
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        public long TotalKeys { get; set; }
        public long TotalMemoryUsage { get; set; }
        public long HitCount { get; set; }
        public long MissCount { get; set; }
        public double HitRate { get; set; }
        public TimeSpan Uptime { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }
} 