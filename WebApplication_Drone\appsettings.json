{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "WebApplication_Drone.Services": "Debug", "System.Net.Http.HttpClient": "Warning"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=AspireApp;Trusted_Connection=true;TrustServerCertificate=true;", "Redis": ""}, "DroneService": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "CacheExpirationMinutes": 10, "EnableRealTimeUpdates": true, "PerformanceMonitoringInterval": 300, "MaxConcurrentOperations": 10}, "TaskService": {"MaxRetryAttempts": 3, "CacheExpirationMinutes": 15, "MaxConcurrentOperations": 8, "EnableRealTimeUpdates": true, "BatchSize": 50}, "SocketService": {"DefaultHost": "*************", "DefaultPort": 5007, "MaxRetries": 5, "RetryIntervalSeconds": 30, "AutoReconnect": true, "MaxQueueSize": 1000, "ConnectionTimeoutSeconds": 30, "KeepAliveInterval": 60}, "SocketConfiguration": {"PythonHost": "*************", "PythonPort": 5007, "ConnectionTimeoutSeconds": 30, "MaxRetries": 5, "RetryIntervalMs": 5000, "AutoReconnect": true, "HeartbeatIntervalSeconds": 30, "MaxQueueSize": 1000}, "MissionSocketService": {"Port": 5010, "MaxConcurrentClients": 50, "DefaultReceiveTimeoutMs": 120000, "LargeFileTimeoutMs": 300000, "DefaultBufferSize": 65536, "LargeFileBufferSize": 131072, "LargeFileThreshold": 1048576}, "Database": {"CommandTimeoutSeconds": 30, "MaxRetryAttempts": 3, "RetryDelaySeconds": 1, "EnableDetailedErrors": false, "ConnectionPoolSize": 100, "EnableSensitiveDataLogging": false}, "Cache": {"DefaultExpirationMinutes": 10, "SlidingExpirationMinutes": 5, "MaxSizeLimit": 1000, "CompactionPercentage": 0.1, "EnableDistributedCache": false}, "DataSource": {"DataSourceType": 0, "CacheExpiryMinutes": 10, "EnableCache": true, "EnableDatabase": true, "WarmupStrategy": 0, "DatabaseTimeoutSeconds": 60, "EnableDetailedLogging": false}, "HealthChecks": {"EnableDetailedChecks": true, "TimeoutSeconds": 30, "DatabaseCheckInterval": 60, "ServiceCheckInterval": 30}, "Performance": {"EnableMetricsCollection": true, "MetricsCollectionInterval": 300, "EnableGCMetrics": true, "EnableBusinessMetrics": true, "LogPerformanceWarnings": true, "MemoryThresholdMB": 1024, "CpuThresholdPercent": 80}, "Features": {"EnableSwagger": true, "EnableDetailedErrors": false, "EnableCors": true, "EnableCompression": true, "EnableRateLimiting": false, "EnableApiVersioning": false}, "RateLimit": {"RequestLimit": 100, "TimeWindowMinutes": 1, "EnableRateLimiting": false, "WhitelistedIPs": ["127.0.0.1", "::1", "*************"], "EndpointLimits": {"GET:/health": 500, "GET:/alive": 500, "GET:/ready": 500, "GET:/api/system/health": 200, "POST:/api/system/gc": 5, "POST:/api/system/force-gc": 2}}}