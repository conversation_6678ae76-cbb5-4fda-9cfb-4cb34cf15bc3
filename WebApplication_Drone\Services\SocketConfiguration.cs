using System.ComponentModel.DataAnnotations;

namespace WebApplication_Drone.Services
{
    /// <summary>
    /// Socket通信配置
    /// </summary>
    public class SocketConfiguration
    {
        /// <summary>
        /// Python后端主机地址
        /// </summary>
        [Required]
        public string PythonHost { get; set; } = "*************";

        /// <summary>
        /// Python后端端口
        /// </summary>
        [Range(1, 65535)]
        public int PythonPort { get; set; } = 5007;

        /// <summary>
        /// 连接超时时间（秒）
        /// </summary>
        [Range(1, 300)]
        public int ConnectionTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Range(0, 10)]
        public int MaxRetries { get; set; } = 5;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        [Range(100, 60000)]
        public int RetryIntervalMs { get; set; } = 5000;

        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool AutoReconnect { get; set; } = true;

        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        [Range(5, 300)]
        public int HeartbeatIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// 发送队列最大容量
        /// </summary>
        [Range(10, 10000)]
        public int MaxQueueSize { get; set; } = 1000;
    }

    /// <summary>
    /// 消息发送结果
    /// </summary>
    public class MessageSendResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// 连接状态
    /// </summary>
    public enum SocketConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Reconnecting,
        Failed
    }
}
