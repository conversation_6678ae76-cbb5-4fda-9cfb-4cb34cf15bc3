using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Diagnostics;
using WebApplication_Drone.Services;

namespace WebApplication_Drone.Controllers
{
    /// <summary>
    /// 系统监控和健康检查控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SystemController : ControllerBase
    {
        private readonly DroneDataService _droneDataService;
        private readonly TaskDataService _taskDataService;
        private readonly HealthCheckService _healthCheckService;
        private readonly PerformanceMonitoringService? _performanceService;
        private readonly ILogger<SystemController> _logger;

        public SystemController(
            DroneDataService droneDataService,
            TaskDataService taskDataService,
            HealthCheckService healthCheckService,
            ILogger<SystemController> logger,
            PerformanceMonitoringService? performanceService = null)
        {
            _droneDataService = droneDataService;
            _taskDataService = taskDataService;
            _healthCheckService = healthCheckService;
            _performanceService = performanceService;
            _logger = logger;
        }

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> GetHealthStatus()
        {
            try
            {
                var report = await _healthCheckService.CheckHealthAsync();
                return Ok(new
                {
                    status = report.Status.ToString(),
                    checks = report.Entries.ToDictionary(
                        entry => entry.Key,
                        entry => new
                        {
                            status = entry.Value.Status.ToString(),
                            description = entry.Value.Description,
                            duration = entry.Value.Duration
                        })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统健康状态失败");
                return StatusCode(500, new { error = "获取系统健康状态失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取系统性能指标
        /// </summary>
        [HttpGet("performance")]
        public IActionResult GetPerformanceMetrics()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                
                var metrics = new
                {
                    // 内存使用情况
                    memory = new
                    {
                        workingSet = process.WorkingSet64,
                        privateMemory = process.PrivateMemorySize64,
                        virtualMemory = process.VirtualMemorySize64,
                        gcGen0Collections = GC.CollectionCount(0),
                        gcGen1Collections = GC.CollectionCount(1),
                        gcGen2Collections = GC.CollectionCount(2),
                        totalMemory = GC.GetTotalMemory(false)
                    },
                    
                    // CPU使用情况
                    cpu = new
                    {
                        totalProcessorTime = process.TotalProcessorTime.TotalMilliseconds,
                        userProcessorTime = process.UserProcessorTime.TotalMilliseconds,
                        privilegedProcessorTime = process.PrivilegedProcessorTime.TotalMilliseconds,
                        threadCount = process.Threads.Count
                    },
                    
                    // 系统信息
                    system = new
                    {
                        startTime = process.StartTime,
                        uptime = DateTime.Now - process.StartTime,
                        processId = process.Id,
                        processName = process.ProcessName,
                        machineName = Environment.MachineName,
                        osVersion = Environment.OSVersion.ToString(),
                        processorCount = Environment.ProcessorCount
                    },
                    
                    // 应用程序指标
                    application = new
                    {
                        droneStats = _droneDataService.GetStatistics(),
                        taskStats = _taskDataService.GetStatistics()
                    },
                    
                    timestamp = DateTime.UtcNow
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标失败");
                return StatusCode(500, new { error = "获取性能指标失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取当前性能指标
        /// </summary>
        [HttpGet("current-metrics")]
        public IActionResult GetCurrentMetrics()
        {
            try
            {
                if (_performanceService == null)
                {
                    return NotFound(new { error = "性能监控服务未启用" });
                }

                var currentMetrics = _performanceService.GetCurrentMetrics();
                if (currentMetrics == null)
                {
                    return NotFound(new { error = "暂无性能数据" });
                }

                return Ok(currentMetrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前性能指标失败");
                return StatusCode(500, new { error = "获取当前性能指标失败", message = ex.Message });
            }
        }

        [HttpGet("drone-stats")]
        public IActionResult GetDroneStats()
        {
            try
            {
                var stats = _droneDataService.GetStatistics();
                return Ok(new DroneServiceStatistics
                {
                    TotalDrones = stats.TotalDrones,
                    OnlineDrones = stats.OnlineDrones,
                    OfflineDrones = stats.OfflineDrones,
                    BusyDrones = stats.OnlineDrones - stats.OfflineDrones,
                    LastUpdate = stats.LastUpdateTime
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取无人机统计数据失败");
                return StatusCode(500, new { error = "获取无人机统计数据失败", message = ex.Message });
            }
        }

        [HttpGet("task-stats")]
        public IActionResult GetTaskStats()
        {
            try
            {
                var stats = _taskDataService.GetStatistics();
                return Ok(new TaskServiceStatistics
                {
                    TotalTasks = stats.TotalTasks,
                    ActiveTasks = stats.ActiveTasks,
                    CompletedTasks = stats.CompletedTasks,
                    PendingTasks = stats.TotalTasks - stats.ActiveTasks - stats.CompletedTasks,
                    FailedTasks = 0, // 需要添加失败任务的统计
                    LastUpdate = stats.LastUpdateTime
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务统计数据失败");
                return StatusCode(500, new { error = "获取任务统计数据失败", message = ex.Message });
            }
        }
    }

    public class DroneServiceStatistics
    {
        public int TotalDrones { get; set; }
        public int OnlineDrones { get; set; }
        public int OfflineDrones { get; set; }
        public int BusyDrones { get; set; }
        public DateTime LastUpdate { get; set; }
    }

    public class TaskServiceStatistics
    {
        public int TotalTasks { get; set; }
        public int ActiveTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int FailedTasks { get; set; }
        public int PendingTasks { get; set; }
        public DateTime LastUpdate { get; set; }
    }
} 