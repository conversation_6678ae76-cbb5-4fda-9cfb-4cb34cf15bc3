<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>75b5e574-4a24-43e8-9d23-33b8484178b0</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.SqlServer" Version="9.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.2.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.11" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AspireApp.ServiceDefaults\AspireApp.ServiceDefaults.csproj" />
    <ProjectReference Include="..\ClassLibrary_Core\ClassLibrary_Core.csproj" />
  </ItemGroup>

</Project>
