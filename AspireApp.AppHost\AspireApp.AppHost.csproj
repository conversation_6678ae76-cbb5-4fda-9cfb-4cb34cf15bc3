﻿<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>724772dd-7640-4981-ab2f-6ce79fd4772a</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.3.1" />
    <PackageReference Include="Aspire.Hosting.SqlServer" Version="9.3.1" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlazorApp_Web\BlazorApp_Web\BlazorApp_Web.csproj" />
    <ProjectReference Include="..\WebApplication_Drone\WebApplication_Drone.csproj" />
  </ItemGroup>

</Project>
