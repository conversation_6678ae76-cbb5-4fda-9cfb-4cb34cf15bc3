<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\BlazorApp_Web.Client\BlazorApp_Web.Client.csproj" />
    <ProjectReference Include="..\..\AspireApp.ServiceDefaults\AspireApp.ServiceDefaults.csproj" />
    <ProjectReference Include="..\..\ClassLibrary_Core\ClassLibrary_Core.csproj" />

    <PackageReference Include="LiveChartsCore.SkiaSharpView" Version="2.0.0-rc5.4" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.Blazor" Version="2.0.0-rc5.4" />

    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.2.0" />
  </ItemGroup>

</Project>
