using ClassLibrary_Core.Drone;
using ClassLibrary_Core.Message;
using ClassLibrary_Core.Mission;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;

namespace WebApplication_Drone.Services
{
    /// <summary>
    /// 增强的Socket通信服务
    /// </summary>
    public class EnhancedSocketService : IDisposable
    {
        private readonly SocketConfiguration _config;
        private readonly ILogger<EnhancedSocketService> _logger;
        private readonly TaskDataService _taskDataService;
        private readonly DroneDataService _droneDataService;

        private TcpClient? _client;
        private NetworkStream? _stream;
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        
        // 连接状态
        private SocketConnectionStatus _connectionStatus = SocketConnectionStatus.Disconnected;
        private DateTime _lastConnectionAttempt = DateTime.MinValue;
        private int _currentRetryCount = 0;

        // 消息队列
        private readonly ConcurrentQueue<Message_Send> _sendQueue = new();
        private readonly SemaphoreSlim _sendSemaphore = new(1, 1);

        // 统计信息
        private long _messagesSent = 0;
        private long _messagesReceived = 0;
        private long _connectionAttempts = 0;
        private DateTime _lastHeartbeat = DateTime.MinValue;

        public EnhancedSocketService(
            IOptions<SocketConfiguration> config,
            ILogger<EnhancedSocketService> logger,
            TaskDataService taskDataService,
            DroneDataService droneDataService)
        {
            _config = config.Value;
            _logger = logger;
            _taskDataService = taskDataService;
            _droneDataService = droneDataService;

            // 订阅事件
            _droneDataService.DroneChanged += OnDroneChanged;
            _taskDataService.TaskChanged += OnTaskChanged;

            // 启动后台任务
            _ = Task.Run(ConnectionManagerAsync);
            _ = Task.Run(MessageProcessorAsync);
            _ = Task.Run(HeartbeatAsync);
        }

        /// <summary>
        /// 连接状态
        /// </summary>
        public SocketConnectionStatus ConnectionStatus => _connectionStatus;

        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected => _connectionStatus == SocketConnectionStatus.Connected && 
                                   _client?.Connected == true && 
                                   _stream?.CanWrite == true;

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public object GetStatistics()
        {
            return new
            {
                ConnectionStatus = _connectionStatus.ToString(),
                MessagesSent = _messagesSent,
                MessagesReceived = _messagesReceived,
                ConnectionAttempts = _connectionAttempts,
                QueueSize = _sendQueue.Count,
                LastHeartbeat = _lastHeartbeat,
                IsConnected = IsConnected
            };
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        public async Task<MessageSendResult> SendMessageAsync(Message_Send message)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                if (_sendQueue.Count >= _config.MaxQueueSize)
                {
                    return new MessageSendResult
                    {
                        Success = false,
                        ErrorMessage = "发送队列已满",
                        Duration = DateTime.UtcNow - startTime
                    };
                }

                _sendQueue.Enqueue(message);
                _logger.LogDebug("消息已加入发送队列: {MessageType}", message.type);

                return new MessageSendResult
                {
                    Success = true,
                    Duration = DateTime.UtcNow - startTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败: {Message}", ex.Message);
                return new MessageSendResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// 连接管理器
        /// </summary>
        private async Task ConnectionManagerAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (!IsConnected && _connectionStatus != SocketConnectionStatus.Connecting)
                    {
                        await AttemptConnectionAsync();
                    }

                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "连接管理器异常: {Message}", ex.Message);
                    await Task.Delay(5000, _cancellationTokenSource.Token);
                }
            }
        }

        /// <summary>
        /// 尝试连接
        /// </summary>
        private async Task AttemptConnectionAsync()
        {
            if (_currentRetryCount >= _config.MaxRetries)
            {
                _connectionStatus = SocketConnectionStatus.Failed;
                _logger.LogError("连接重试次数已达上限: {MaxRetries}", _config.MaxRetries);
                return;
            }

            var timeSinceLastAttempt = DateTime.UtcNow - _lastConnectionAttempt;
            if (timeSinceLastAttempt < TimeSpan.FromMilliseconds(_config.RetryIntervalMs))
            {
                return;
            }

            _connectionStatus = SocketConnectionStatus.Connecting;
            _lastConnectionAttempt = DateTime.UtcNow;
            _connectionAttempts++;

            try
            {
                _logger.LogInformation("尝试连接到Python后端: {Host}:{Port} (第{Attempt}次尝试)", 
                    _config.PythonHost, _config.PythonPort, _currentRetryCount + 1);

                // 清理旧连接
                CleanupConnection();

                // 创建新连接
                _client = new TcpClient();
                _client.ReceiveTimeout = _config.ConnectionTimeoutSeconds * 1000;
                _client.SendTimeout = _config.ConnectionTimeoutSeconds * 1000;

                await _client.ConnectAsync(_config.PythonHost, _config.PythonPort);
                _stream = _client.GetStream();

                _connectionStatus = SocketConnectionStatus.Connected;
                _currentRetryCount = 0;
                _lastHeartbeat = DateTime.UtcNow;

                _logger.LogInformation("成功连接到Python后端: {Host}:{Port}", _config.PythonHost, _config.PythonPort);

                // 启动接收消息任务
                _ = Task.Run(ReceiveMessagesAsync);

                // 发送初始化消息
                await SendMessageAsync(new Message_Send { content = "50", type = "start_all" });
            }
            catch (Exception ex)
            {
                _currentRetryCount++;
                _connectionStatus = SocketConnectionStatus.Disconnected;
                _logger.LogWarning(ex, "连接失败 (第{Attempt}次尝试): {Message}", _currentRetryCount, ex.Message);
                
                CleanupConnection();
            }
        }

        /// <summary>
        /// 清理连接
        /// </summary>
        private void CleanupConnection()
        {
            try
            {
                _stream?.Close();
                _client?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "清理连接时出现异常: {Message}", ex.Message);
            }
            finally
            {
                _stream = null;
                _client = null;
            }
        }

        /// <summary>
        /// 消息处理器
        /// </summary>
        private async Task MessageProcessorAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_sendQueue.TryDequeue(out var message) && IsConnected)
                    {
                        await SendMessageDirectlyAsync(message);
                    }
                    else
                    {
                        await Task.Delay(100, _cancellationTokenSource.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "消息处理器异常: {Message}", ex.Message);
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }
        }

        /// <summary>
        /// 直接发送消息
        /// </summary>
        private async Task SendMessageDirectlyAsync(Message_Send message)
        {
            await _sendSemaphore.WaitAsync(_cancellationTokenSource.Token);
            
            try
            {
                if (!IsConnected)
                {
                    _sendQueue.Enqueue(message); // 重新入队
                    return;
                }

                var json = JsonSerializer.Serialize(message);
                var data = Encoding.UTF8.GetBytes(json);

                await _stream!.WriteAsync(data, _cancellationTokenSource.Token);
                await _stream.FlushAsync(_cancellationTokenSource.Token);

                Interlocked.Increment(ref _messagesSent);
                _logger.LogDebug("消息发送成功: {MessageType}, 内容长度: {Length}", message.type, data.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送消息失败: {MessageType}, 错误: {Message}", message.type, ex.Message);
                _connectionStatus = SocketConnectionStatus.Disconnected;
                _sendQueue.Enqueue(message); // 重新入队
            }
            finally
            {
                _sendSemaphore.Release();
            }
        }

        /// <summary>
        /// 心跳任务
        /// </summary>
        private async Task HeartbeatAsync()
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (IsConnected)
                    {
                        var timeSinceLastHeartbeat = DateTime.UtcNow - _lastHeartbeat;
                        if (timeSinceLastHeartbeat >= TimeSpan.FromSeconds(_config.HeartbeatIntervalSeconds))
                        {
                            await SendMessageAsync(new Message_Send { content = "", type = "node_info" });
                            _lastHeartbeat = DateTime.UtcNow;
                        }
                    }

                    await Task.Delay(TimeSpan.FromSeconds(_config.HeartbeatIntervalSeconds), _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "心跳任务异常: {Message}", ex.Message);
                }
            }
        }

        /// <summary>
        /// 接收消息
        /// </summary>
        private async Task ReceiveMessagesAsync()
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            while (IsConnected && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var bytesRead = await _stream!.ReadAsync(buffer, 0, buffer.Length, _cancellationTokenSource.Token);

                    if (bytesRead == 0)
                    {
                        _logger.LogWarning("Python后端主动断开连接");
                        _connectionStatus = SocketConnectionStatus.Disconnected;
                        break;
                    }

                    messageBuffer.AddRange(buffer.Take(bytesRead));

                    // 尝试解析完整的JSON消息
                    while (TryParseMessage(messageBuffer, out var message, out var bytesConsumed))
                    {
                        messageBuffer.RemoveRange(0, bytesConsumed);
                        ProcessReceivedMessage(message);
                        Interlocked.Increment(ref _messagesReceived);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "接收消息时出错: {Message}", ex.Message);
                    _connectionStatus = SocketConnectionStatus.Disconnected;
                    break;
                }
            }
        }

        /// <summary>
        /// 尝试解析消息
        /// </summary>
        private bool TryParseMessage(List<byte> buffer, out Message? message, out int bytesConsumed)
        {
            message = null;
            bytesConsumed = 0;

            try
            {
                var json = Encoding.UTF8.GetString(buffer.ToArray());
                using var document = JsonDocument.Parse(json);

                message = JsonSerializer.Deserialize<Message>(json);
                bytesConsumed = buffer.Count;
                return true;
            }
            catch (JsonException)
            {
                // JSON不完整，等待更多数据
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析消息失败: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private void ProcessReceivedMessage(Message message)
        {
            try
            {
                _logger.LogDebug("收到消息: {MessageType}", message.type);

                switch (message.type)
                {
                    case "ans_node_info":
                    case "start_success":
                        HandleNodeInfo(message.content);
                        break;
                    case "tasks_info":
                        HandleTasksInfo(message.content);
                        break;
                    case "Subtasks_info":
                        HandleSubtasksInfo(message.content);
                        break;
                    case "cluster_info":
                        HandleClusterInfo(message);
                        break;
                    case "reassign_info":
                        HandleReassignInfo(message.content);
                        break;
                    default:
                        _logger.LogWarning("未知消息类型: {MessageType}", message.type);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理消息时发生错误: {MessageType}, {Message}", message.type, ex.Message);
            }
        }

        /// <summary>
        /// 处理节点信息
        /// </summary>
        private void HandleNodeInfo(Dictionary<string, List<object>> content)
        {
            try
            {
                var drones = ParseDronesFromJson(content);
                _droneDataService.SetDrones(drones);
                _logger.LogDebug("更新了 {Count} 个无人机信息", drones.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理节点信息失败: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// 处理任务信息
        /// </summary>
        private void HandleTasksInfo(Dictionary<string, List<object>> content)
        {
            // 实现任务信息处理逻辑
            _logger.LogDebug("处理任务信息: {Count} 个节点", content.Count);
        }

        /// <summary>
        /// 处理子任务信息
        /// </summary>
        private void HandleSubtasksInfo(Dictionary<string, List<object>> content)
        {
            // 实现子任务信息处理逻辑
            _logger.LogDebug("处理子任务信息: {Count} 个组", content.Count);
        }

        /// <summary>
        /// 处理集群信息
        /// </summary>
        private void HandleClusterInfo(Message message)
        {
            // 实现集群信息处理逻辑
            _logger.LogDebug("处理集群信息");
        }

        /// <summary>
        /// 处理重新分配信息
        /// </summary>
        private void HandleReassignInfo(Dictionary<string, List<object>> content)
        {
            // 实现重新分配信息处理逻辑
            _logger.LogDebug("处理重新分配信息");
        }

        /// <summary>
        /// 解析无人机信息
        /// </summary>
        private List<Drone> ParseDronesFromJson(Dictionary<string, List<object>> content)
        {
            var drones = new List<Drone>();

            try
            {
                if (!content.ContainsKey("nodes_name")) return drones;

                var nodesName = content["nodes_name"].ConvertAll(x => ((JsonElement)x).GetString());
                var dealSpeed = content.GetValueOrDefault("deal_speed", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var radius = content.GetValueOrDefault("radius", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var memory = content.GetValueOrDefault("memory", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var leftBandwidth = content.GetValueOrDefault("left_bandwidth", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var x = content.GetValueOrDefault("x", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var y = content.GetValueOrDefault("y", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());
                var cpuUsedRate = content.GetValueOrDefault("cpu_used_rate", new List<object>()).ConvertAll(x => ((JsonElement)x).GetDouble());

                for (int i = 0; i < nodesName.Count; i++)
                {
                    var name = nodesName[i];
                    var existingDrone = _droneDataService.GetDroneByName(name);

                    var drone = new Drone
                    {
                        Id = existingDrone?.Id ?? Guid.NewGuid(),
                        Name = name,
                        Status = existingDrone?.Status ?? DroneStatus.Idle,
                        CurrentPosition = new ClassLibrary_Core.Common.GPSPosition(
                            i < x.Count ? x[i] : 0,
                            i < y.Count ? y[i] : 0),
                        cpu_used_rate = i < cpuUsedRate.Count ? cpuUsedRate[i] : 0,
                        radius = i < radius.Count ? radius[i] : 0,
                        memory = i < memory.Count ? memory[i] : 0,
                        left_bandwidth = i < leftBandwidth.Count ? leftBandwidth[i] : 0,
                        ConnectedDroneIds = existingDrone?.ConnectedDroneIds ?? new List<Guid>(),
                        AssignedSubTasks = existingDrone?.AssignedSubTasks ?? new List<SubTask>(),
                        ModelStatus = existingDrone?.ModelStatus ?? ModelStatus.True,
                        ModelType = existingDrone?.ModelType ?? string.Empty
                    };

                    drones.Add(drone);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析无人机信息失败: {Message}", ex.Message);
            }

            return drones;
        }

        /// <summary>
        /// 无人机变更事件处理
        /// </summary>
        private async void OnDroneChanged(object? sender, DroneChangedEventArgs e)
        {
            try
            {
                if (e.Action == "Delete" || (e.Action == "Update" && e.Drone.Status == DroneStatus.Offline))
                {
                    _logger.LogInformation("无人机 {DroneName} 被停用，发送shutdown消息", e.Drone.Name);

                    var result = await SendMessageAsync(new Message_Send
                    {
                        content = e.Drone.Name,
                        type = "shutdown"
                    });

                    if (result.Success)
                    {
                        _logger.LogInformation("成功发送shutdown消息给Python后端: {DroneName}", e.Drone.Name);
                    }
                    else
                    {
                        _logger.LogError("发送shutdown消息失败: {DroneName}, 错误: {Error}", e.Drone.Name, result.ErrorMessage);
                    }
                }
                else
                {
                    _logger.LogDebug("无人机状态更新: {DroneName} -> {Status}", e.Drone.Name, e.Drone.Status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理无人机变更事件失败: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// 任务变更事件处理
        /// </summary>
        private async void OnTaskChanged(object? sender, TaskChangedEventArgs e)
        {
            try
            {
                if (e.Action == "Add")
                {
                    _logger.LogInformation("新任务添加: {TaskDescription}", e.MainTask.Description);

                    var result = await SendMessageAsync(new Message_Send
                    {
                        content = e.MainTask.Description,
                        type = "create_tasks",
                        next_node = e.MainTask.Id.ToString()
                    });

                    if (result.Success)
                    {
                        _logger.LogInformation("成功发送任务创建消息: {TaskDescription}", e.MainTask.Description);
                    }
                    else
                    {
                        _logger.LogError("发送任务创建消息失败: {TaskDescription}, 错误: {Error}",
                            e.MainTask.Description, result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务变更事件失败: {Message}", ex.Message);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource.Cancel();
            CleanupConnection();
            _cancellationTokenSource.Dispose();
            _sendSemaphore.Dispose();
        }
    }
}
