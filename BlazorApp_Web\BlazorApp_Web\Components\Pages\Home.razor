﻿@page "/"
@using Microsoft.AspNetCore.SignalR.Client
@using BlazorApp_Web.Service
@using ClassLibrary_Core.Data
@using ClassLibrary_Core.Drone
@using ClassLibrary_Core.Mission
@using Microsoft.AspNetCore.Components.Forms
@using System.ComponentModel.DataAnnotations
@inject IHttpClientFactory HttpClientFactory
@inject NavigationManager Navigation
@inject ILogger<Home> Logger
@inject SystemStatisticsService SystemStats
@implements IAsyncDisposable

<PageTitle>基于分散计算的动态负载均衡任务分配原型系统 - 首页</PageTitle>

<div class="dashboard-home">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
        <div class="banner-content">
            <div class="welcome-text">
                <h1 class="welcome-title">欢迎使用</h1>
                <p class="welcome-subtitle">实时监控、智能调度、高效管理</p>
                <div class="quick-stats">
                    <span class="stat-item">
                        <i class="bi bi-check-circle text-success"></i>
                        系统运行@systemHealth
                    </span>
                    <span class="stat-item">
                        <i class="bi bi-clock"></i>
                        运行时间: @SystemStats.GetUptime(startTime)
                    </span>
                </div>
            </div>
            <div class="banner-animation">
                <div class="drone-icon">
                    <i class="bi bi-robot"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <div class="stat-menu">
                    <i class="bi bi-three-dots"></i>
                </div>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">@(droneStats?.TotalDrones ?? 0)</h3>
                <p class="stat-label">无人机总数</p>
                <div class="stat-trend @(droneStats?.OnlineDrones > lastDroneOnline ? "up" : "down")">
                    <i class="bi @(droneStats?.OnlineDrones > lastDroneOnline ? "bi-arrow-up" : "bi-arrow-down")"></i>
                    <span>@(droneStats?.OnlineDrones > lastDroneOnline ? "+" : "-")@(Math.Abs((droneStats?.OnlineDrones ?? 0) - lastDroneOnline))</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="active-count">@(droneStats?.OnlineDrones ?? 0) 台在线</span>
            </div>
        </div>

        <div class="stat-card success">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-kanban"></i>
                </div>
                <div class="stat-menu">
                    <i class="bi bi-three-dots"></i>
                </div>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">@(taskStats?.TotalTasks ?? 0)</h3>
                <p class="stat-label">总任务数</p>
                <div class="stat-trend @(taskStats?.ActiveTasks > lastActiveTasks ? "up" : "down")">
                    <i class="bi @(taskStats?.ActiveTasks > lastActiveTasks ? "bi-arrow-up" : "bi-arrow-down")"></i>
                    <span>@(taskStats?.ActiveTasks > lastActiveTasks ? "+" : "-")@(Math.Abs((taskStats?.ActiveTasks ?? 0) - lastActiveTasks))</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="active-count">@(taskStats?.ActiveTasks ?? 0) 个进行中</span>
            </div>
        </div>

        <div class="stat-card warning">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-menu">
                    <i class="bi bi-three-dots"></i>
                </div>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">@(taskStats?.CompletedTasks ?? 0)</h3>
                <p class="stat-label">已完成任务</p>
                <div class="stat-trend @(taskStats?.CompletedTasks > lastCompletedTasks ? "up" : "down")">
                    <i class="bi @(taskStats?.CompletedTasks > lastCompletedTasks ? "bi-arrow-up" : "bi-arrow-down")"></i>
                    <span>@(taskStats?.CompletedTasks > lastCompletedTasks ? "+" : "-")@(Math.Abs((taskStats?.CompletedTasks ?? 0) - lastCompletedTasks))</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="active-count">今日完成 @todayCompletedTasks 个</span>
            </div>
        </div>

        <div class="stat-card info">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="bi bi-graph-up"></i>
                </div>
                <div class="stat-menu">
                    <i class="bi bi-three-dots"></i>
                </div>
            </div>
            <div class="stat-content">
                <h3 class="stat-number">@string.Format("{0:0.0}", performanceMetrics?.CpuUsagePercent ?? 0)%</h3>
                <p class="stat-label">系统CPU使用率</p>
                <div class="stat-trend @(performanceMetrics?.CpuUsagePercent > lastCpuUsage ? "up" : "down")">
                    <i class="bi @(performanceMetrics?.CpuUsagePercent > lastCpuUsage ? "bi-arrow-up" : "bi-arrow-down")"></i>
                    <span>@string.Format("{0:0.0}", (performanceMetrics?.CpuUsagePercent ?? 0) - lastCpuUsage)%</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="active-count">内存使用: @(performanceMetrics?.MemoryUsageMB ?? 0)MB</span>
            </div>
        </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-sections">
        <!-- 左侧：快速操作 -->
        <div class="section-left">
            <div class="card quick-actions-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="bi bi-lightning"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="action-grid">
                        <a href="/task_manage" class="action-btn">
                            <div class="action-icon">
                                <i class="bi bi-plus-circle"></i>
                            </div>
                            <span class="action-text">创建任务</span>
                        </a>
                        
                        <a href="/drone_map" class="action-btn">
                            <div class="action-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <span class="action-text">查看地图</span>
                        </a>
                        
                        <a href="/history-analysis" class="action-btn">
                            <div class="action-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <span class="action-text">数据分析</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 系统状态 -->
            <div class="card system-status-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="bi bi-speedometer2"></i>
                        系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="status-items">
                        <div class="status-item">
                            <div class="status-label">
                                <i class="bi bi-cpu"></i>
                                CPU使用率
                            </div>
                            <div class="status-value">
                                <span class="status-number">@string.Format("{0:0.0}", performanceMetrics?.CpuUsagePercent ?? 0)%</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: @(performanceMetrics?.CpuUsagePercent ?? 0)%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-label">
                                <i class="bi bi-memory"></i>
                                内存使用
                            </div>
                            <div class="status-value">
                                <!-- 
                                <span class="status-number">@(performanceMetrics?.MemoryUsageMB ?? 0)MB</span>
                                -->
                                <div class="progress-bar">
                                    <!--
                                    <div class="progress-fill" style="width: @(((performanceMetrics?.MemoryUsageMB ?? 0) / (performanceMetrics?.TotalMemoryBytes / 1024 / 1024) * 100).ToString("F1"))%"></div>
                                    -->
                                </div>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">
                                <i class="bi bi-hdd"></i>
                                线程数
                            </div>
                            <div class="status-value">
                                <span class="status-number">@(performanceMetrics?.ThreadCount ?? 0)</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: @(Math.Min((performanceMetrics?.ThreadCount ?? 0) / 100.0 * 100, 100))%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：最近活动和通知 -->
        <div class="section-right">
            <div class="card recent-activity-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="bi bi-clock-history"></i>
                        最近活动
                    </h5>
                    <button class="btn btn-sm btn-outline-primary">查看全部</button>
                </div>
                <div class="card-body">
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">任务 #001 已成功完成</p>
                                <span class="activity-time">2分钟前</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon primary">
                                <i class="bi bi-robot"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">无人机 Drone-05 已上线</p>
                                <span class="activity-time">5分钟前</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">检测到系统负载较高</p>
                                <span class="activity-time">10分钟前</span>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon info">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">系统维护将于今晚进行</p>
                                <span class="activity-time">1小时前</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Socket连接监控 -->
            <div class="mt-3">
                <SocketMonitor />
            </div>
        </div>
    </div>

    <!-- 快速创建任务对话框 -->
    <div class="modal @(showCreateDialog ? "show d-block" : "d-none")" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">快速创建任务</h5>
                    <button type="button" class="btn-close" @onclick="CloseCreateDialog"></button>
                </div>
                <EditForm Model="@taskModel" OnValidSubmit="@HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="taskName" class="form-label">任务名称</label>
                            <InputText id="taskName" class="form-control" @bind-Value="taskModel.TaskName" />
                            <ValidationMessage For="@(() => taskModel.TaskName)" />
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">任务描述</label>
                            <InputTextArea id="description" class="form-control" @bind-Value="taskModel.Description" />
                            <ValidationMessage For="@(() => taskModel.Description)" />
                        </div>
                        <div class="mb-3">
                            <label for="videoFile" class="form-label">视频文件</label>
                            <InputFile id="videoFile" OnChange="@HandleFileSelected" class="form-control" accept=".mp4,.avi,.mov" />
                            @if (!string.IsNullOrEmpty(fileError))
                            {
                                <div class="text-danger mt-1">@fileError</div>
                            }
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseCreateDialog">取消</button>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                                <span>提交中...</span>
                            }
                            else
                            {
                                <span>创建任务</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>

    @if (showCreateDialog)
    {
        <div class="modal-backdrop show"></div>
    }
</div>

<style>
    .dashboard-home {
        padding: 1rem;
    }

    .welcome-banner {
        background: linear-gradient(135deg, #4a6cf7 0%, #2651f0 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }

    .banner-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .welcome-text {
        flex: 1;
    }

    .welcome-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .welcome-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .quick-stats {
        margin-top: 1rem;
    }

    .stat-item {
        display: inline-flex;
        align-items: center;
        margin-right: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
    }

    .stat-item i {
        margin-right: 0.5rem;
    }

    .banner-animation {
        flex: 0 0 auto;
    }

    .drone-icon {
        font-size: 4rem;
        animation: float-animation 3s ease-in-out infinite;
    }

    @@keyframes float-animation {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .stat-card.primary { border-top: 4px solid #4a6cf7; }
    .stat-card.success { border-top: 4px solid #2ecc71; }
    .stat-card.warning { border-top: 4px solid #f1c40f; }
    .stat-card.info { border-top: 4px solid #3498db; }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .stat-icon i {
        font-size: 1.5rem;
        color: #4a6cf7;
    }

    .stat-content {
        text-align: center;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        margin-bottom: 1rem;
    }

    .stat-trend {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
    }

    .stat-trend.up {
        background-color: rgba(46, 204, 113, 0.1);
        color: #2ecc71;
    }

    .stat-trend.down {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }

    .stat-trend i {
        margin-right: 0.25rem;
    }

    .stat-footer {
        text-align: center;
        color: #666;
        font-size: 0.875rem;
    }

    .main-sections {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 1.5rem;
    }

    .quick-actions-card,
    .system-status-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #eee;
    }

    .card-title {
        margin: 0;
        display: flex;
        align-items: center;
        font-size: 1.25rem;
    }

    .card-title i {
        margin-right: 0.5rem;
        color: #4a6cf7;
    }

    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
        color: #007bff;
        text-decoration: none;
    }

    .action-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #007bff;
    }

    .action-text {
        font-size: 1rem;
        font-weight: 500;
    }

    .status-items {
        padding: 1.5rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .status-item:last-child {
        margin-bottom: 0;
    }

    .status-label {
        display: flex;
        align-items: center;
        color: #666;
    }

    .status-label i {
        margin-right: 0.5rem;
        color: #4a6cf7;
    }

    .status-value {
        text-align: right;
    }

    .status-number {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .progress-bar {
        width: 100px;
        height: 6px;
        background-color: #eee;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background-color: #4a6cf7;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .progress-fill.warning {
        background-color: #f1c40f;
    }

    .modal {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .action-card {
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .card-body {
        padding: 2rem;
    }

    .fas {
        color: #007bff;
    }

    .card-title {
        margin-top: 1rem;
        font-weight: 600;
    }

    .card-text {
        color: #6c757d;
    }
</style>

@code {
    private HubConnection? hubConnection;
    private readonly DateTime startTime = DateTime.Now;
    private string systemHealth = "未知";
    
    // 统计数据
    private SystemStatisticsService.DroneServiceStatistics? droneStats;
    private SystemStatisticsService.TaskServiceStatistics? taskStats;
    private SystemStatisticsService.PerformanceMetrics? performanceMetrics;
    
    // 上一次的统计数据，用于计算趋势
    private int lastDroneOnline = 0;
    private int lastActiveTasks = 0;
    private int lastCompletedTasks = 0;
    private double lastCpuUsage = 0;
    private int todayCompletedTasks = 0;
    
    private Timer? refreshTimer;
    private bool showCreateDialog = false;
    private bool isSubmitting = false;
    private string? fileError;
    private IBrowserFile? selectedFile;

    private TaskModel taskModel = new();

    private class TaskModel
    {
        [Required(ErrorMessage = "请输入任务名称")]
        [StringLength(50, ErrorMessage = "任务名称不能超过50个字符")]
        public string TaskName { get; set; } = "";

        [Required(ErrorMessage = "请输入任务描述")]
        [StringLength(200, ErrorMessage = "任务描述不能超过200个字符")]
        public string Description { get; set; } = "";
    }

    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(Navigation.ToAbsoluteUri("/dronehub"))
            .WithAutomaticReconnect()
            .Build();

        await hubConnection.StartAsync();
        
        // 加载统计数据
        await LoadStatistics();
        
        // 设置定时刷新
        refreshTimer = new Timer(async _ => 
        {
            await LoadStatistics();
            await InvokeAsync(StateHasChanged);
        }, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
    }

    private async Task LoadStatistics()
    {
        try
        {
            // 获取系统健康状态
            systemHealth = await SystemStats.GetSystemHealthAsync();
            
            // 获取无人机统计
            var newDroneStats = await SystemStats.GetDroneStatisticsAsync();
            if (newDroneStats != null)
            {
                lastDroneOnline = droneStats?.OnlineDrones ?? 0;
                droneStats = newDroneStats;
                Logger.LogDebug("无人机统计更新: 总数={Total}, 在线={Online}, 离线={Offline}", 
                    droneStats.TotalDrones, droneStats.OnlineDrones, droneStats.OfflineDrones);
            }
            
            // 获取任务统计
            var newTaskStats = await SystemStats.GetTaskStatisticsAsync();
            if (newTaskStats != null)
            {
                lastActiveTasks = taskStats?.ActiveTasks ?? 0;
                lastCompletedTasks = taskStats?.CompletedTasks ?? 0;
                taskStats = newTaskStats;
                
                // 获取今日完成的任务数
                todayCompletedTasks = await SystemStats.GetTodayCompletedTasksAsync();
                Logger.LogDebug("任务统计更新: 总数={Total}, 活跃={Active}, 完成={Completed}, 今日完成={Today}", 
                    taskStats.TotalTasks, taskStats.ActiveTasks, taskStats.CompletedTasks, todayCompletedTasks);
            }
            
            // 获取性能指标
            var newPerformanceMetrics = await SystemStats.GetPerformanceMetricsAsync();
            if (newPerformanceMetrics != null)
            {
                lastCpuUsage = performanceMetrics?.CpuUsagePercent ?? 0;
                performanceMetrics = newPerformanceMetrics;
                Logger.LogDebug("性能指标更新: CPU={CPU}%, 内存={Memory}MB", 
                    performanceMetrics.CpuUsagePercent, performanceMetrics.MemoryUsageMB);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载统计数据失败");
        }
    }

    private void CreateTask()
    {
        Navigation.NavigateTo("/task_manage", forceLoad: true);
    }

    private void ViewMap()
    {
        Navigation.NavigateTo("/drone_map", forceLoad: true);
    }

    private void ViewAnalysis()
    {
        Navigation.NavigateTo("/history-analysis", forceLoad: true);
    }

    private void CloseCreateDialog()
    {
        showCreateDialog = false;
    }

    private void HandleFileSelected(InputFileChangeEventArgs e)
    {
        fileError = null;
        var file = e.File;
        
        if (file.Size > 1024 * 1024 * 500) // 500MB
        {
            fileError = "文件大小不能超过500MB";
            return;
        }

        var allowedExtensions = new[] { ".mp4", ".avi", ".mov" };
        var extension = Path.GetExtension(file.Name).ToLowerInvariant();
        
        if (!allowedExtensions.Contains(extension))
        {
            fileError = "只支持MP4、AVI、MOV格式的视频文件";
            return;
        }

        selectedFile = file;
    }

    private async Task HandleValidSubmit()
    {
        if (selectedFile == null)
        {
            fileError = "请选择视频文件";
            return;
        }

        try
        {
            isSubmitting = true;

            var client = HttpClientFactory.CreateClient("ApiService");
            
            // 创建任务数据
            var taskData = new
            {
                Name = taskModel.TaskName,
                Description = taskModel.Description
            };

            // 创建表单数据
            var content = new MultipartFormDataContent();
            var taskJson = new StringContent(System.Text.Json.JsonSerializer.Serialize(taskData));
            content.Add(taskJson, "task");
            
            // 添加视频文件
            var fileContent = new StreamContent(selectedFile.OpenReadStream());
            content.Add(fileContent, "video", selectedFile.Name);

            // 发送请求
            var response = await client.PostAsync("api/tasks/video", content);
            if (response.IsSuccessStatusCode)
            {
                // 创建成功，跳转到任务管理页面
                Navigation.NavigateTo("/task_manage");
            }
            else
            {
                // 处理错误
                Logger.LogError("创建任务失败");
                fileError = "创建任务失败，请重试";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "创建任务失败");
            fileError = "创建任务时发生错误，请重试";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (refreshTimer != null)
        {
            await refreshTimer.DisposeAsync();
        }
        
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }
}
