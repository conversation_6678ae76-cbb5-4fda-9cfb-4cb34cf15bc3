﻿@page "/history-analysis"
@rendermode InteractiveServer
@using System.Globalization
@using BlazorApp_Web.Service
@using ClassLibrary_Core.Data
@using ClassLibrary_Core.Drone
@using ClassLibrary_Core.Mission
@implements IDisposable
@inject HistoryApiService HistoryApiService
@inject IHttpClientFactory HttpClientFactory
@inject ILogger<HistoryDataAnalysis> Logger
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-3">
        <div class="col-12">
            <h3 class="mb-0">分散资源与任务分配历史数据分析</h3>
            <p class="text-muted">实时监控和分析分散资源状态和任务分配的历史数据、执行情况和性能指标</p>
        </div>
    </div>

    <!-- 系统概览卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <SystemOverviewCard 
                Overview="@systemOverview"
                OnRefresh="@RefreshOverview" />
        </div>
    </div>

    <!-- 主要功能选项卡 -->
    <div class="row">
        <div class="col-12">
            <TabNavigation ActiveTab="@activeTab" OnTabChanged="@SetActiveTab">
                @if (activeTab == "drone")
                {
                    <DroneDataAnalysis 
                        OnDataLoaded="@LoadDroneData"
                        AvailableDrones="@DataDrones"
                        AvailableTasks="@DataTasks"
                        DataPoints="@DataPoints" />
                }
                else if (activeTab == "task")
                {
                    <TaskDataAnalysis 
                        OnDataLoaded="@LoadTaskData"
                        AvailableDrones="@DataDrones"
                        AvailableTasks="@DataTasks"
                        TaskData="@TaskSubTasks" />
                }
                else if (activeTab == "time")
                {
                    <EnhancedTimeRangeAnalysis 
                        OnAnalysisCompleted="@HandleTimeRangeAnalysisCompleted" />
                }
                else if (activeTab == "statistics")
                {
                    <StatisticsAnalysisTab 
                        TaskStatistics="@taskStatistics"
                        PerformanceAnalysis="@performanceAnalysis"
                        ExpiredTasks="@expiredTasks"
                        OnLoadExpiredTasks="@LoadExpiredTasks" />
                }
                else if (activeTab == "management")
                {
                    <DataManagementTab 
                        IsLoading="@isLoading"
                        CurrentOperation="@currentOperation"
                        Message="@managementMessage"
                        IsSuccess="@managementSuccess"
                        OnLoadFromDatabase="@LoadFromDatabase"
                        OnSyncToDatabase="@SyncToDatabase"
                        OnReassignFailedTasks="@ReassignFailedTasks"
                        OnCleanupOldTasks="@CleanupOldTasks"
                        OnClearMessage="@ClearManagementMessage" />
                }
            </TabNavigation>
        </div>
    </div>
</div>

@code {
    // 页面状态
    private string activeTab = "drone";
    private bool isLoading = false;
    private string currentOperation = "";
    private string managementMessage = "";
    private bool managementSuccess = false;

    // 数据属性
    private List<Drone> DataDrones { get; set; } = new();
    private List<MainTask> DataTasks { get; set; } = new();
    private List<SubTask> TaskSubTasks { get; set; } = new();
    private List<DroneDataPoint> DataPoints { get; set; } = new();
    private List<SubTask> expiredTasks { get; set; } = new();
    
    // 统计和分析数据
    private SystemOverview? systemOverview;
    private TaskStatistics? taskStatistics;
    private TaskPerformanceAnalysis? performanceAnalysis;
    private TimeRangeData? currentTimeRangeAnalysis;

    private Timer? refreshTimer;

    #region 生命周期方法

    protected override async Task OnInitializedAsync()
    {
        await Task.WhenAll(
            LoadInitialData(),
            RefreshOverview(),
            LoadStatisticsData()
        );
        
        // 启动定时器，每30秒刷新一次数据
        refreshTimer = new Timer(async _ => await RefreshData(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    public void Dispose()
    {
        refreshTimer?.Dispose();
    }

    private async Task RefreshData()
    {
        try
        {
            await InvokeAsync(async () =>
            {
                await Task.WhenAll(RefreshOverview(), LoadStatisticsData());
                StateHasChanged();
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "自动刷新数据错误");
        }
    }

    #endregion

    #region 数据加载方法

    private async Task LoadInitialData()
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            // 使用实际的API端点
            var droneTask = client.GetFromJsonAsync<List<Drone>>("api/drones");
            var taskTask = client.GetFromJsonAsync<List<MainTask>>("api/tasks");
            
            await Task.WhenAll(droneTask, taskTask);
            
            DataDrones = await droneTask ?? new List<Drone>();
            DataTasks = await taskTask ?? new List<MainTask>();
            
            Logger.LogInformation("初始数据加载完成: 无人机={DroneCount}, 任务={TaskCount}", 
                DataDrones.Count, DataTasks.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载初始数据错误");
            DataDrones = new List<Drone>();
            DataTasks = new List<MainTask>();
        }
    }

    private async Task RefreshOverview()
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            // 获取实际的系统数据
            var drones = await client.GetFromJsonAsync<List<Drone>>("api/drones") ?? new List<Drone>();
            var tasks = await client.GetFromJsonAsync<List<MainTask>>("api/tasks") ?? new List<MainTask>();
            
            // 构建系统概览
            systemOverview = new SystemOverview
            {
                Timestamp = DateTime.Now,
                Drones = new DroneOverview
                {
                    Total = drones.Count,
                    Online = drones.Count(d => d.Status == DroneStatus.Idle || d.Status == DroneStatus.InMission),
                    Offline = drones.Count(d => d.Status == DroneStatus.Offline)
                },
                Tasks = new TaskStatistics
                {
                    TotalMainTasks = tasks.Count,
                    CompletedMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion),
                    ActiveMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Running),
                    FailedMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Faulted),
                    PendingMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Created || 
                                                   t.Status == System.Threading.Tasks.TaskStatus.WaitingForActivation),
                    TotalSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>()).Count(),
                    CompletedSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                        .Count(st => st.Status == System.Threading.Tasks.TaskStatus.RanToCompletion),
                    ActiveSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                        .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Running),
                    FailedSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                        .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Faulted),
                    PendingSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                        .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Created || 
                                    st.Status == System.Threading.Tasks.TaskStatus.WaitingForActivation)
                },
                Performance = new TaskPerformanceAnalysis
                {
                    AverageExecutionTimeMinutes = CalculateAverageExecutionTime(tasks),
                    TotalCompletedTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion),
                    MinExecutionTimeMinutes = tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                                              t.CompletedTime.HasValue).Any() ?
                        tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                        t.CompletedTime.HasValue)
                             .Min(t => (t.CompletedTime!.Value - t.CreationTime).TotalMinutes) : 0,
                    MaxExecutionTimeMinutes = tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                                              t.CompletedTime.HasValue).Any() ?
                        tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                        t.CompletedTime.HasValue)
                             .Max(t => (t.CompletedTime!.Value - t.CreationTime).TotalMinutes) : 0
                }
            };
            
            Logger.LogInformation("系统概览数据刷新完成");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "刷新概览数据错误");
        }
    }

    private async Task LoadStatisticsData()
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            var tasks = await client.GetFromJsonAsync<List<MainTask>>("api/tasks") ?? new List<MainTask>();
            var drones = await client.GetFromJsonAsync<List<Drone>>("api/drones") ?? new List<Drone>();
            
            // 构建任务统计
            taskStatistics = new TaskStatistics
            {
                TotalMainTasks = tasks.Count,
                CompletedMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion),
                ActiveMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Running),
                FailedMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Faulted),
                PendingMainTasks = tasks.Count(t => t.Status == System.Threading.Tasks.TaskStatus.Created || 
                                               t.Status == System.Threading.Tasks.TaskStatus.WaitingForActivation),
                TotalSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>()).Count(),
                CompletedSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                    .Count(st => st.Status == System.Threading.Tasks.TaskStatus.RanToCompletion),
                ActiveSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                    .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Running),
                FailedSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                    .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Faulted),
                PendingSubTasks = tasks.SelectMany(t => t.SubTasks ?? new List<SubTask>())
                    .Count(st => st.Status == System.Threading.Tasks.TaskStatus.Created || 
                                st.Status == System.Threading.Tasks.TaskStatus.WaitingForActivation)
            };
            
            // 构建性能分析
            var completedTasks = tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                                 t.CompletedTime.HasValue).ToList();
            
            performanceAnalysis = new TaskPerformanceAnalysis
            {
                AverageExecutionTimeMinutes = CalculateAverageExecutionTime(tasks),
                TotalCompletedTasks = completedTasks.Count,
                MinExecutionTimeMinutes = completedTasks.Any() ? 
                    completedTasks.Min(t => (t.CompletedTime!.Value - t.CreationTime).TotalMinutes) : 0,
                MaxExecutionTimeMinutes = completedTasks.Any() ? 
                    completedTasks.Max(t => (t.CompletedTime!.Value - t.CreationTime).TotalMinutes) : 0
            };
            
            Logger.LogInformation("统计数据加载完成");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载统计数据错误");
        }
    }

    private double CalculateAverageExecutionTime(List<MainTask> tasks)
    {
        var completedTasks = tasks.Where(t => t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion && 
                                             t.CompletedTime.HasValue).ToList();
        
        if (!completedTasks.Any())
            return 0;
            
        var totalMinutes = completedTasks.Sum(t => (t.CompletedTime!.Value - t.CreationTime).TotalMinutes);
        return totalMinutes / completedTasks.Count;
    }

    #endregion

    #region 选项卡导航

    private async Task SetActiveTab(string tab)
    {
        activeTab = tab;
        
        // 切换到统计页面时刷新数据
        if (tab == "statistics")
        {
            await LoadStatisticsData();
        }
        
        StateHasChanged();
    }

    #endregion

    #region 数据查询方法

    private async Task LoadDroneData(DroneDataRequest request)
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            if (request.model == 1)
            {
                // 获取无人机历史数据
                var endTime = DateTime.Now;
                var startTime = endTime.Subtract(request.timeSpan);
                
                var response = await client.GetFromJsonAsync<List<DroneDataPoint>>(
                    $"api/drones/{request.drone}/data?startTime={startTime:yyyy-MM-ddTHH:mm:ss}&endTime={endTime:yyyy-MM-ddTHH:mm:ss}");
                
                DataPoints = response ?? new List<DroneDataPoint>();
                
                Logger.LogInformation("无人机数据加载完成: DroneId={DroneId}, 数据点={DataPointCount}", 
                    request.drone, DataPoints.Count);
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载无人机数据错误: DroneId={DroneId}", request.drone);
            DataPoints = new List<DroneDataPoint>();
        }
    }

    private async Task LoadTaskData(DroneDataRequest request)
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            if (request.taskId.HasValue)
            {
                // 获取主任务详情
                var mainTask = await client.GetFromJsonAsync<MainTask>($"api/tasks/{request.taskId.Value}");
                
                if (mainTask?.SubTasks != null)
                {
                    TaskSubTasks = mainTask.SubTasks.ToList();
                    Logger.LogInformation("任务数据加载完成: TaskId={TaskId}, 子任务数={SubTaskCount}", 
                        request.taskId.Value, TaskSubTasks.Count);
                }
                else
                {
                    TaskSubTasks = new List<SubTask>();
                }
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载任务数据错误: TaskId={TaskId}", request.taskId);
            TaskSubTasks = new List<SubTask>();
        }
    }

    private async Task LoadTimeRangeData(TimeRangeData request)
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            // 获取指定时间范围内所有无人机的数据
            var response = await client.GetFromJsonAsync<List<DroneDataPoint>>(
                $"api/drones/data/range?startTime={request.StartTime:yyyy-MM-ddTHH:mm:ss}&endTime={request.EndTime:yyyy-MM-ddTHH:mm:ss}");
            
            DataPoints = response ?? new List<DroneDataPoint>();
            
            Logger.LogInformation("时间范围数据加载完成: 开始时间={StartTime}, 结束时间={EndTime}, 数据点={DataPointCount}", 
                request.StartTime, request.EndTime, DataPoints.Count);
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载时间范围数据错误: 开始时间={StartTime}, 结束时间={EndTime}", 
                request.StartTime, request.EndTime);
            DataPoints = new List<DroneDataPoint>();
        }
    }

    private Task RenderTimeRangeChart(List<DroneDataPoint> data)
    {
        // 时间范围图表渲染逻辑
        return Task.CompletedTask;
    }

    private Task HandleTimeRangeAnalysisCompleted(TimeRangeData analysisResult)
    {
        currentTimeRangeAnalysis = analysisResult;
        Console.WriteLine($"时间范围分析完成: {analysisResult.Name}, 记录数: {analysisResult.RecordCount}");
        StateHasChanged();
        return Task.CompletedTask;
    }

    private async Task LoadExpiredTasks(int timeoutMinutes)
    {
        try
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            var tasks = await client.GetFromJsonAsync<List<MainTask>>("api/tasks") ?? new List<MainTask>();
            
            // 查找过期的子任务
            var cutoffTime = DateTime.Now.AddMinutes(-timeoutMinutes);
            expiredTasks = new List<SubTask>();
            
            foreach (var task in tasks)
            {
                if (task.SubTasks != null)
                {
                    var expiredSubTasks = task.SubTasks.Where(st => 
                        st.Status == System.Threading.Tasks.TaskStatus.Running &&
                        st.AssignedTime.HasValue &&
                        st.AssignedTime.Value < cutoffTime).ToList();
                    
                    expiredTasks.AddRange(expiredSubTasks);
                }
            }
            
            Logger.LogInformation("过期任务加载完成: 超时分钟={TimeoutMinutes}, 过期任务数={ExpiredTaskCount}", 
                timeoutMinutes, expiredTasks.Count);
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载过期任务错误: 超时分钟={TimeoutMinutes}", timeoutMinutes);
            expiredTasks = new List<SubTask>();
        }
    }

    #endregion

    #region 数据管理方法

    private async Task LoadFromDatabase()
    {
        await ExecuteManagementOperation("load", async () =>
        {
            // 刷新数据
            await LoadInitialData();
            return "任务数据已从数据库重新加载完成";
        });
    }

    private async Task SyncToDatabase()
    {
        await ExecuteManagementOperation("sync", async () =>
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            
            // 调用同步API（如果存在）
            try
            {
                var response = await client.PostAsync("api/tasks/sync", null);
                response.EnsureSuccessStatusCode();
                return "任务数据已同步到数据库";
            }
            catch
            {
                // 如果没有同步API，则只是刷新数据
                await LoadInitialData();
                return "数据已刷新（同步功能暂未实现）";
            }
        });
    }

    private async Task ReassignFailedTasks()
    {
        await ExecuteManagementOperation("reassign", async () =>
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            var tasks = await client.GetFromJsonAsync<List<MainTask>>("api/tasks") ?? new List<MainTask>();
            
            // 统计失败的子任务
            int failedCount = 0;
            foreach (var task in tasks)
            {
                if (task.SubTasks != null)
                {
                    failedCount += task.SubTasks.Count(st => st.Status == System.Threading.Tasks.TaskStatus.Faulted);
                }
            }
            
            // 这里应该调用重新分配API，暂时只返回统计信息
            return $"发现 {failedCount} 个失败的子任务（重新分配功能需要API支持）";
        });
    }

    private async Task CleanupOldTasks(int cleanupDays)
    {
        await ExecuteManagementOperation("cleanup", async () =>
        {
            var client = HttpClientFactory.CreateClient("ApiService");
            var tasks = await client.GetFromJsonAsync<List<MainTask>>("api/tasks") ?? new List<MainTask>();
            
            // 统计超过指定天数的已完成任务
            var cutoffDate = DateTime.Now.AddDays(-cleanupDays);
            var oldCompletedTasks = tasks.Where(t => 
                t.Status == System.Threading.Tasks.TaskStatus.RanToCompletion &&
                t.CompletedTime.HasValue &&
                t.CompletedTime.Value < cutoffDate).ToList();
            
            // 这里应该调用清理API，暂时只返回统计信息
            return $"发现 {oldCompletedTasks.Count} 个超过 {cleanupDays} 天的已完成任务（清理功能需要API支持）";
        });
    }

    private async Task ExecuteManagementOperation(string operation, Func<Task<string>> action)
    {
        try
        {
            isLoading = true;
            currentOperation = operation;
            StateHasChanged();

            var message = await action();
            
            managementMessage = message;
            managementSuccess = true;
            
            // 刷新相关数据
            await Task.WhenAll(RefreshOverview(), LoadStatisticsData());
        }
        catch (Exception ex)
        {
            managementMessage = $"操作失败: {ex.Message}";
            managementSuccess = false;
        }
        finally
        {
            isLoading = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    private void ClearManagementMessage()
    {
        managementMessage = "";
        StateHasChanged();
    }

    #endregion
}

