{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "WebApplication_Drone.Services": "Debug"}}, "AllowedHosts": "*", "DroneService": {"MaxRetryAttempts": 3, "RetryDelay": "00:00:02", "CacheExpirationMinutes": 10, "EnableRealTimeUpdates": true}, "SocketService": {"DefaultHost": "*************", "DefaultPort": 5007, "MaxRetries": 5, "RetryInterval": "00:00:30", "AutoReconnect": true, "MaxQueueSize": 1000}, "Database": {"CommandTimeoutSeconds": 30, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Redis": {"DefaultExpiration": "01:00:00", "KeyPrefix": "AspireApp:", "EnableCompression": true}, "OpenTelemetry": {"ServiceName": "AspireApp.DroneService", "ServiceVersion": "1.0.0"}}