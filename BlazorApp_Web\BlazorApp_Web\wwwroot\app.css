/* 现代化全局样式 */
* {
    box-sizing: border-box;
}

html, body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: #f8f9fa;
    color: #2c3e50;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a, .btn-link {
    color: #667eea;
    text-decoration: none;
    transition: all 0.2s ease;
}

a:hover, .btn-link:hover {
    color: #764ba2;
    text-decoration: none;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    line-height: 1.5;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* 表单控件样式 */
.form-control {
    display: block;
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

/* 焦点样式 */
.btn:focus, 
.btn:active:focus, 
.btn-link.nav-link:focus, 
.form-control:focus, 
.form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
    outline: none;
}

/* 内容区域 */
.content {
    padding: 0;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.4;
    margin-top: 0;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

h1:focus {
    outline: none;
}

/* 表单验证样式 */
.valid.modified:not([type=checkbox]) {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.validation-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 模态框遮罩 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    backdrop-filter: blur(4px);
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* 徽章组件 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    line-height: 1;
}

.badge-primary {
    background: #667eea;
    color: white;
}

.badge-success {
    background: #28a745;
    color: white;
}

.badge-warning {
    background: #ffc107;
    color: #212529;
}

.badge-danger {
    background: #dc3545;
    color: white;
}

.badge-info {
    background: #17a2b8;
    color: white;
}

/* 进度条 */
.progress {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

/* 表格样式 */
.table {
    width: 100%;
    margin-bottom: 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: #f8f9fa;
    border: none;
    padding: 16px;
    font-weight: 600;
    color: #495057;
}

.table tbody td {
    padding: 16px;
    border-top: 1px solid #e9ecef;
}

.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* 错误边界样式 */
.blazor-error-boundary {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
    border-radius: 8px;
    margin: 16px;
    position: relative;
}

.blazor-error-boundary::before {
    content: "⚠️";
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
}

.blazor-error-boundary::after {
    content: "应用程序发生错误，请刷新页面重试。";
}

/* 文本颜色工具类 */
.text-primary { color: #667eea !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: #28a745 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #17a2b8 !important; }
.text-muted { color: #6c757d !important; }

/* 背景颜色工具类 */
.bg-primary { background-color: #667eea !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-light { background-color: #f8f9fa !important; }
.bg-dark { background-color: #343a40 !important; }

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* 显示工具类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* Flexbox 工具类 */
.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

/* 圆角工具类 */
.rounded { border-radius: 8px !important; }
.rounded-sm { border-radius: 4px !important; }
.rounded-lg { border-radius: 12px !important; }
.rounded-xl { border-radius: 16px !important; }
.rounded-circle { border-radius: 50% !important; }

/* 阴影工具类 */
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; }
.shadow { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important; }
.shadow-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important; }

/* 复选框样式 */
.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #dee2e6;
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.darker-border-checkbox.form-check-input {
    border-color: #929292;
}

/* 响应式工具类 */
@media (max-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
}

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
}

@media (max-width: 992px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 选择文本颜色 */
::selection {
    background: rgba(102, 126, 234, 0.2);
    color: #2c3e50;
}

