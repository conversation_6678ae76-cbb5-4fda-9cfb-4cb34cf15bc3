using Microsoft.AspNetCore.Mvc;
using WebApplication_Drone.Middleware.Interfaces;
using WebApplication_Drone.Services.Interfaces;

namespace WebApplication_Drone.Controllers
{
    /// <summary>
    /// 数据源管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DataSourceController : ControllerBase
    {
        private readonly IDataSourceMiddleware _dataSourceMiddleware;
        private readonly ILogger<DataSourceController> _logger;

        public DataSourceController(
            IDataSourceMiddleware dataSourceMiddleware,
            ILogger<DataSourceController> logger)
        {
            _dataSourceMiddleware = dataSourceMiddleware;
            _logger = logger;
        }

        /// <summary>
        /// 获取数据源状态
        /// </summary>
        /// <returns>数据源状态信息</returns>
        [HttpGet("status")]
        public ActionResult<DataSourceStatus> GetStatus()
        {
            try
            {
                var status = _dataSourceMiddleware.GetDataSourceStatus();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据源状态失败");
                return StatusCode(500, new { error = "获取数据源状态失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取数据源配置
        /// </summary>
        /// <returns>数据源配置信息</returns>
        [HttpGet("config")]
        public ActionResult<DataSourceConfig> GetConfig()
        {
            try
            {
                var config = _dataSourceMiddleware.GetDataSourceConfig();
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据源配置失败");
                return StatusCode(500, new { error = "获取数据源配置失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 更新数据源配置
        /// </summary>
        /// <param name="config">新的配置</param>
        /// <returns>操作结果</returns>
        [HttpPut("config")]
        public async Task<ActionResult> UpdateConfig([FromBody] DataSourceConfig config)
        {
            try
            {
                await _dataSourceMiddleware.UpdateDataSourceConfigAsync(config);
                _logger.LogInformation("数据源配置更新成功");
                return Ok(new { message = "配置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新数据源配置失败");
                return StatusCode(500, new { error = "更新数据源配置失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 切换数据源类型
        /// </summary>
        /// <param name="dataSourceType">数据源类型</param>
        /// <returns>操作结果</returns>
        [HttpPost("switch/{dataSourceType}")]
        public async Task<ActionResult> SwitchDataSource(DataSourceType dataSourceType)
        {
            try
            {
                await _dataSourceMiddleware.SwitchDataSourceAsync(dataSourceType);
                _logger.LogInformation("数据源切换成功：{DataSourceType}", dataSourceType);
                return Ok(new { message = $"数据源已切换到：{dataSourceType}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换数据源失败：{DataSourceType}", dataSourceType);
                return StatusCode(500, new { error = "切换数据源失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 刷新缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("cache/refresh")]
        public async Task<ActionResult> RefreshCache()
        {
            try
            {
                await _dataSourceMiddleware.RefreshCacheAsync();
                _logger.LogInformation("缓存刷新成功");
                return Ok(new { message = "缓存刷新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新缓存失败");
                return StatusCode(500, new { error = "刷新缓存失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("cache/clear")]
        public async Task<ActionResult> ClearCache()
        {
            try
            {
                await _dataSourceMiddleware.ClearCacheAsync();
                _logger.LogInformation("缓存清空成功");
                return Ok(new { message = "缓存清空成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空缓存失败");
                return StatusCode(500, new { error = "清空缓存失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 预热缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("cache/warmup")]
        public async Task<ActionResult> WarmupCache()
        {
            try
            {
                await _dataSourceMiddleware.WarmupCacheAsync();
                _logger.LogInformation("缓存预热成功");
                return Ok(new { message = "缓存预热成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预热缓存失败");
                return StatusCode(500, new { error = "预热缓存失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 检查数据源健康状态
        /// </summary>
        /// <returns>健康状态信息</returns>
        [HttpGet("health")]
        public async Task<ActionResult<DataSourceHealthStatus>> CheckHealth()
        {
            try
            {
                var health = await _dataSourceMiddleware.CheckHealthAsync();
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查数据源健康状态失败");
                return StatusCode(500, new { error = "检查健康状态失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        [HttpGet("cache/statistics")]
        public async Task<ActionResult<CacheStatistics>> GetCacheStatistics()
        {
            try
            {
                var stats = await _dataSourceMiddleware.GetCacheStatisticsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存统计信息失败");
                return StatusCode(500, new { error = "获取缓存统计失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取数据源类型列表
        /// </summary>
        /// <returns>数据源类型列表</returns>
        [HttpGet("types")]
        public ActionResult<object> GetDataSourceTypes()
        {
            try
            {
                var types = Enum.GetValues<DataSourceType>()
                    .Select(t => new { 
                        value = (int)t, 
                        name = t.ToString(),
                        description = GetDataSourceTypeDescription(t)
                    });
                return Ok(types);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据源类型列表失败");
                return StatusCode(500, new { error = "获取数据源类型失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取缓存预热策略列表
        /// </summary>
        /// <returns>缓存预热策略列表</returns>
        [HttpGet("warmup-strategies")]
        public ActionResult<object> GetWarmupStrategies()
        {
            try
            {
                var strategies = Enum.GetValues<CacheWarmupStrategy>()
                    .Select(s => new { 
                        value = (int)s, 
                        name = s.ToString(),
                        description = GetWarmupStrategyDescription(s)
                    });
                return Ok(strategies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取预热策略列表失败");
                return StatusCode(500, new { error = "获取预热策略失败", message = ex.Message });
            }
        }

        private string GetDataSourceTypeDescription(DataSourceType type)
        {
            return type switch
            {
                DataSourceType.DatabaseFirst => "数据库优先，缓存作为加速层",
                DataSourceType.CacheFirst => "缓存优先，数据库作为备份",
                DataSourceType.DatabaseOnly => "仅使用数据库，禁用缓存",
                DataSourceType.CacheOnly => "仅使用缓存，禁用数据库",
                DataSourceType.Hybrid => "混合模式，智能选择数据源",
                _ => "未知类型"
            };
        }

        private string GetWarmupStrategyDescription(CacheWarmupStrategy strategy)
        {
            return strategy switch
            {
                CacheWarmupStrategy.OnDemand => "按需预热",
                CacheWarmupStrategy.OnStartup => "启动时预热",
                CacheWarmupStrategy.Scheduled => "定时预热",
                CacheWarmupStrategy.None => "不预热",
                _ => "未知策略"
            };
        }
    }
} 