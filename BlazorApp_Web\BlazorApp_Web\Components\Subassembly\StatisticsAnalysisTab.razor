@using ClassLibrary_Core.Data
@using ClassLibrary_Core.Mission

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6>任务统计</h6>
            </div>
            <div class="card-body">
                @if (TaskStatistics != null)
                {
                    <table class="table table-sm">
                        <tr><td>总主任务数:</td><td><strong>@TaskStatistics.TotalMainTasks</strong></td></tr>
                        <tr><td>总子任务数:</td><td><strong>@TaskStatistics.TotalSubTasks</strong></td></tr>
                        <tr><td>活跃主任务:</td><td><span class="text-success">@TaskStatistics.ActiveMainTasks</span></td></tr>
                        <tr><td>已完成主任务:</td><td><span class="text-primary">@TaskStatistics.CompletedMainTasks</span></td></tr>
                        <tr><td>失败主任务:</td><td><span class="text-danger">@TaskStatistics.FailedMainTasks</span></td></tr>
                        <tr><td>主任务完成率:</td><td><strong>@TaskStatistics.MainTaskCompletionRate.ToString("F1")%</strong></td></tr>
                        <tr><td>子任务完成率:</td><td><strong>@TaskStatistics.SubTaskCompletionRate.ToString("F1")%</strong></td></tr>
                    </table>
                }
                else
                {
                    <p class="text-muted">正在加载统计数据...</p>
                }
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6>性能分析</h6>
            </div>
            <div class="card-body">
                @if (PerformanceAnalysis != null)
                {
                    <table class="table table-sm">
                        <tr><td>已完成任务数:</td><td><strong>@PerformanceAnalysis.TotalCompletedTasks</strong></td></tr>
                        <tr><td>平均执行时间:</td><td><strong>@PerformanceAnalysis.AverageExecutionTimeMinutes.ToString("F1") 分钟</strong></td></tr>
                        <tr><td>最短执行时间:</td><td>@PerformanceAnalysis.MinExecutionTimeMinutes.ToString("F1") 分钟</td></tr>
                        <tr><td>最长执行时间:</td><td>@PerformanceAnalysis.MaxExecutionTimeMinutes.ToString("F1") 分钟</td></tr>
                        <tr><td>执行时间跨度:</td><td>@PerformanceAnalysis.ExecutionTimeRange.ToString("F1") 分钟</td></tr>
                        <tr><td>效率评级:</td><td><span class="badge badge-@GetEfficiencyBadgeClass(PerformanceAnalysis.EfficiencyRating)">@PerformanceAnalysis.EfficiencyRating</span></td></tr>
                    </table>
                }
                else
                {
                    <p class="text-muted">正在加载性能数据...</p>
                }
            </div>
        </div>
    </div>
</div>

<!-- 过期任务监控 -->
<div class="row mt-3">
    <div class="col-12">
        <ExpiredTasksMonitor 
            ExpiredTasks="@ExpiredTasks"
            OnLoadExpiredTasks="@OnLoadExpiredTasks" />
    </div>
</div>

@code {
    [Parameter] public TaskStatistics? TaskStatistics { get; set; }
    [Parameter] public TaskPerformanceAnalysis? PerformanceAnalysis { get; set; }
    [Parameter] public List<SubTask> ExpiredTasks { get; set; } = new();
    [Parameter] public EventCallback<int> OnLoadExpiredTasks { get; set; }

    private string GetEfficiencyBadgeClass(string rating)
    {
        return rating switch
        {
            "优秀" => "success",
            "良好" => "primary",
            "一般" => "warning",
            _ => "danger"
        };
    }
} 