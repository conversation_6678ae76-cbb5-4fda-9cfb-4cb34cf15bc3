// 性能监控图表管理
let charts = {};

// 初始化所有图表
window.initializeCharts = function() {
    initChart('cpuChart');
    initChart('memoryChart');
    initChart('bandwidthChart');
    initChart('onlineChart');
};

// 初始化单个图表
function initChart(chartId) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // 销毁已存在的图表
    if (charts[chartId]) {
        charts[chartId].destroy();
    }

    charts[chartId] = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '',
                data: [],
                borderColor: '#3498db',
                backgroundColor: '#3498db20',
                fill: true,
                tension: 0.4,
                pointRadius: 2,
                pointHoverRadius: 6,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '数值'
                    },
                    beginAtZero: true
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 500,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// 更新图表数据
window.updateChart = function(chartId, chartData) {
    const chart = charts[chartId];
    if (!chart) return;

    chart.data.labels = chartData.labels;
    chart.data.datasets[0].label = chartData.datasets[0].label;
    chart.data.datasets[0].data = chartData.datasets[0].data;
    chart.data.datasets[0].borderColor = chartData.datasets[0].borderColor;
    chart.data.datasets[0].backgroundColor = chartData.datasets[0].backgroundColor;
    
    chart.update('none'); // 快速更新，不使用动画
};

// 清理图表资源
window.destroyCharts = function() {
    Object.values(charts).forEach(chart => {
        if (chart) {
            chart.destroy();
        }
    });
    charts = {};
}; 