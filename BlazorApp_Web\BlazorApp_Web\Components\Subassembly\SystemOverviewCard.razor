@using BlazorApp_Web.Service
@using ClassLibrary_Core.Data

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">系统概览</h5>
        <button class="btn btn-outline-primary btn-sm" @onclick="OnRefresh">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
    <div class="card-body">
        @if (Overview != null)
        {
            <div class="row">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h4 class="text-primary">@Overview.Drones.Total</h4>
                            <p class="card-text">总无人机数</p>
                            <small class="text-muted">在线: @Overview.Drones.Online | 离线: @Overview.Drones.Offline</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h4 class="text-success">@Overview.Tasks.TotalMainTasks</h4>
                            <p class="card-text">总任务数</p>
                            <small class="text-muted">完成率: @Overview.Tasks.MainTaskCompletionRate.ToString("F1")%</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h4 class="text-info">@Overview.Tasks.ActiveSubTasks</h4>
                            <p class="card-text">活跃子任务</p>
                            <small class="text-muted">待处理: @Overview.Tasks.PendingSubTasks</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h4 class="text-warning">@Overview.Performance.EfficiencyRating</h4>
                            <p class="card-text">系统效率</p>
                            <small class="text-muted">平均执行时间: @Overview.Performance.AverageExecutionTimeMinutes.ToString("F1")分钟</small>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2">正在加载系统概览...</p>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public SystemOverview? Overview { get; set; }
    [Parameter] public EventCallback OnRefresh { get; set; }
} 