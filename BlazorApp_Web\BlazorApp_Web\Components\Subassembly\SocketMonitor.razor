@using System.Text.Json
@inject IHttpClientFactory HttpClientFactory
@inject ILogger<SocketMonitor> <PERSON><PERSON>
@implements IDisposable

<div class="socket-monitor">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Socket连接监控</h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" @onclick="RefreshStatus" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm me-1"></span>
                    }
                    刷新
                </button>
                <button class="btn btn-sm btn-outline-secondary" @onclick="SendTestMessage" disabled="@isLoading">
                    测试连接
                </button>
            </div>
        </div>
        <div class="card-body">
            @if (connectionStatus != null)
            {
                <div class="row">
                    <div class="col-md-6">
                        <h6>连接状态</h6>
                        <div class="status-indicator">
                            <span class="badge @GetStatusBadgeClass(connectionStatus.IsConnected)">
                                @(connectionStatus.IsConnected ? "已连接" : "未连接")
                            </span>
                            <small class="text-muted ms-2">@connectionStatus.ConnectionStatus</small>
                        </div>
                        <small class="text-muted d-block mt-1">
                            最后更新: @connectionStatus.Timestamp.ToString("HH:mm:ss")
                        </small>
                    </div>
                    <div class="col-md-6">
                        <h6>操作</h6>
                        @if (!string.IsNullOrEmpty(lastTestResult))
                        {
                            <div class="alert alert-info alert-sm">
                                @lastTestResult
                            </div>
                        }
                    </div>
                </div>
            }

            @if (statistics != null)
            {
                <hr />
                <h6>统计信息</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value">@statistics.MessagesSent</div>
                            <div class="stat-label">已发送消息</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value">@statistics.MessagesReceived</div>
                            <div class="stat-label">已接收消息</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value">@statistics.ConnectionAttempts</div>
                            <div class="stat-label">连接尝试</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-value">@statistics.QueueSize</div>
                            <div class="stat-label">队列大小</div>
                        </div>
                    </div>
                </div>
            }

            @if (errorMessage != null)
            {
                <div class="alert alert-danger mt-3">
                    <strong>错误:</strong> @errorMessage
                </div>
            }
        </div>
    </div>
</div>

<style>
    .socket-monitor .status-indicator {
        display: flex;
        align-items: center;
    }

    .socket-monitor .stat-item {
        text-align: center;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        background-color: #f8f9fa;
    }

    .socket-monitor .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #495057;
    }

    .socket-monitor .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .socket-monitor .alert-sm {
        padding: 0.375rem 0.75rem;
        margin-bottom: 0;
        font-size: 0.875rem;
    }
</style>

@code {
    private Timer? refreshTimer;
    private bool isLoading = false;
    private string? errorMessage;
    private string? lastTestResult;

    private ConnectionStatus? connectionStatus;
    private SocketStatistics? statistics;

    protected override async Task OnInitializedAsync()
    {
        await RefreshStatus();
        
        // 每5秒自动刷新状态
        refreshTimer = new Timer(async _ => await InvokeAsync(RefreshStatus), null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
    }

    private async Task RefreshStatus()
    {
        if (isLoading) return;

        try
        {
            isLoading = true;
            errorMessage = null;
            StateHasChanged();

            var client = HttpClientFactory.CreateClient("ApiService");

            // 获取连接状态
            var statusResponse = await client.GetAsync("api/SocketMonitor/status");
            if (statusResponse.IsSuccessStatusCode)
            {
                var statusJson = await statusResponse.Content.ReadAsStringAsync();
                connectionStatus = JsonSerializer.Deserialize<ConnectionStatus>(statusJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }

            // 获取统计信息
            var statsResponse = await client.GetAsync("api/SocketMonitor/statistics");
            if (statsResponse.IsSuccessStatusCode)
            {
                var statsJson = await statsResponse.Content.ReadAsStringAsync();
                statistics = JsonSerializer.Deserialize<SocketStatistics>(statsJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "刷新Socket状态失败: {Message}", ex.Message);
            errorMessage = $"刷新失败: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SendTestMessage()
    {
        try
        {
            isLoading = true;
            lastTestResult = null;
            StateHasChanged();

            var client = HttpClientFactory.CreateClient("ApiService");
            var response = await client.PostAsync("api/SocketMonitor/test-message?messageType=node_info&content=", null);

            if (response.IsSuccessStatusCode)
            {
                var resultJson = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<TestMessageResult>(resultJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                lastTestResult = result?.Success == true 
                    ? $"测试消息发送成功 (耗时: {result.Duration:F1}ms)"
                    : $"测试消息发送失败: {result?.ErrorMessage}";
            }
            else
            {
                lastTestResult = $"测试失败: HTTP {response.StatusCode}";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "发送测试消息失败: {Message}", ex.Message);
            lastTestResult = $"测试失败: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetStatusBadgeClass(bool isConnected)
    {
        return isConnected ? "bg-success" : "bg-danger";
    }

    public void Dispose()
    {
        refreshTimer?.Dispose();
    }

    // 数据模型
    private class ConnectionStatus
    {
        public bool IsConnected { get; set; }
        public string ConnectionStatus { get; set; } = "";
        public DateTime Timestamp { get; set; }
    }

    private class SocketStatistics
    {
        public long MessagesSent { get; set; }
        public long MessagesReceived { get; set; }
        public long ConnectionAttempts { get; set; }
        public int QueueSize { get; set; }
        public DateTime LastHeartbeat { get; set; }
    }

    private class TestMessageResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public double Duration { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
