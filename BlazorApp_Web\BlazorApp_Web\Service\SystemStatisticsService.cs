using System.Text.Json;
using ClassLibrary_Core.Data;
using ClassLibrary_Core.Mission;

namespace BlazorApp_Web.Service
{
    public class SystemStatisticsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SystemStatisticsService> _logger;

        public SystemStatisticsService(IHttpClientFactory httpClientFactory, ILogger<SystemStatisticsService> logger)
        {
            _httpClient = httpClientFactory.CreateClient("ApiService");
            _logger = logger;
        }

        public class DroneServiceStatistics
        {
            public int TotalDrones { get; set; }
            public int OnlineDrones { get; set; }
            public int OfflineDrones { get; set; }
            public int BusyDrones { get; set; }
            public DateTime LastUpdate { get; set; }
        }

        public class TaskServiceStatistics
        {
            public int TotalTasks { get; set; }
            public int ActiveTasks { get; set; }
            public int CompletedTasks { get; set; }
            public int FailedTasks { get; set; }
            public int PendingTasks { get; set; }
            public DateTime LastUpdate { get; set; }
        }

        public class PerformanceMetrics
        {
            public double CpuUsagePercent { get; set; }
            public double MemoryUsageMB { get; set; }
            public int ThreadCount { get; set; }
            public long TotalMemoryBytes { get; set; }
            public DateTime Timestamp { get; set; }
        }

        public async Task<DroneServiceStatistics?> GetDroneStatisticsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/system/drone-stats");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<DroneServiceStatistics>(content);
                }
                _logger.LogWarning("获取无人机统计数据失败: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取无人机统计数据时发生错误");
                return null;
            }
        }

        public async Task<TaskServiceStatistics?> GetTaskStatisticsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/system/task-stats");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<TaskServiceStatistics>(content);
                }
                _logger.LogWarning("获取任务统计数据失败: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务统计数据时发生错误");
                return null;
            }
        }

        public async Task<PerformanceMetrics?> GetPerformanceMetricsAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/system/performance");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<PerformanceMetrics>(content);
                }
                _logger.LogWarning("获取性能指标失败: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取性能指标时发生错误");
                return null;
            }
        }

        public async Task<int> GetTodayCompletedTasksAsync()
        {
            try
            {
                var today = DateTime.Today;
                var response = await _httpClient.GetAsync($"api/tasks/completed?startTime={today:yyyy-MM-dd}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tasks = JsonSerializer.Deserialize<List<MainTask>>(content);
                    return tasks?.Count ?? 0;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取今日完成任务数时发生错误");
                return 0;
            }
        }

        public async Task<string> GetSystemHealthAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/system/health");
                return response.IsSuccessStatusCode ? "正常" : "异常";
            }
            catch
            {
                return "未知";
            }
        }

        public string GetUptime(DateTime startTime)
        {
            var uptime = DateTime.Now - startTime;
            return uptime.Days > 0 
                ? $"{uptime.Days}天 {uptime.Hours}小时" 
                : $"{uptime.Hours}小时 {uptime.Minutes}分钟";
        }
    }
} 