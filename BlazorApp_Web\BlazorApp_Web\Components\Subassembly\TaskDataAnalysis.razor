@using ClassLibrary_Core.Data
@using ClassLibrary_Core.Drone
@using ClassLibrary_Core.Mission
@using static ClassLibrary_Core.Mission.SubTask
@using LiveChartsCore
@using LiveChartsCore.SkiaSharpView
@using LiveChartsCore.SkiaSharpView.Painting
@using SkiaSharp

<div class="card">
    <div class="card-header">
        <h5>任务历史数据分析</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="form-label">选择任务:</label>
                    <select class="form-select" @bind="SelectedTaskId">
                        <option value="">请选择任务</option>
                        @foreach (var task in AvailableTasks)
                        {
                            <option value="@task.Id">@task.Description (@task.Status)</option>
                        }
                    </select>
                </div>

                <div class="form-group mb-3">
                    <label class="form-label">分析维度:</label>
                    <select class="form-select" @bind="AnalysisType">
                        <option value="single">单无人机数据</option>
                        <option value="all">所有无人机数据</option>
                        <option value="subtasks">子任务分析</option>
                        <option value="performance">性能对比</option>
                    </select>
                </div>

                @if (AnalysisType == "single")
                {
                    <div class="form-group mb-3">
                        <label class="form-label">选择无人机:</label>
                        <select class="form-select" @bind="SelectedDroneId">
                            <option value="">请选择无人机</option>
                            @foreach (var drone in AvailableDrones)
                            {
                                <option value="@drone.Id">@drone.Name (@drone.ModelType)</option>
                            }
                        </select>
                    </div>
                }

                <div class="d-grid gap-2">
                    <button class="btn btn-primary" @onclick="LoadData" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        分析任务数据
                    </button>
                    
                    @if (TaskData?.Any() == true)
                    {
                        <button class="btn btn-outline-success" @onclick="ExportTaskReport">
                            <i class="fas fa-file-excel me-1"></i>导出报告
                        </button>
                    }
                </div>

                <!-- 任务统计概览 -->
                @if (TaskData?.Any() == true)
                {
                    <div class="mt-4">
                        <h6>任务概览</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">总子任务</small>
                                        <div class="h5 mb-0">@TaskData.Count</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-success bg-opacity-10">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">已完成</small>
                                        <div class="h5 mb-0 text-success">@TaskData.Count(t => t.Status == TaskStatus.RanToCompletion)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- 右侧数据显示 -->
            <div class="col-md-8">
                @if (TaskData?.Any() == true)
                {
                    <!-- 详细数据表格 -->
                    <div class="table-responsive">
                        <h6>任务执行详情</h6>
                        <table class="table table-sm table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>子任务ID</th>
                                    <th>描述</th>
                                    <th>分配无人机</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var task in TaskData.Take(50))
                                {
                                    <tr>
                                        <td>
                                            <small class="font-monospace">@task.Id.ToString().Substring(0, 8)...</small>
                                        </td>
                                        <td>@task.Description</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(task.AssignedDrone))
                                            {
                                                <span class="badge bg-info">@task.AssignedDrone</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">未分配</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-@GetStatusBadgeClass(task.Status.ToString())">
                                                @task.Status
                                            </span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else if (isLoading)
                {
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-3">正在分析任务数据...</p>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <p class="text-muted">请选择任务和分析维度，然后点击"分析任务数据"</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public EventCallback<DroneDataRequest> OnDataLoaded { get; set; }
    [Parameter] public List<Drone> AvailableDrones { get; set; } = new();
    [Parameter] public List<MainTask> AvailableTasks { get; set; } = new();
    [Parameter] public List<SubTask> TaskData { get; set; } = new();

    private string SelectedTaskId { get; set; } = "";
    private string SelectedDroneId { get; set; } = "";
    private string AnalysisType { get; set; } = "all";
    private bool isLoading = false;

    private async Task LoadData()
    {
        if (string.IsNullOrEmpty(SelectedTaskId))
        {
            return;
        }

        isLoading = true;
        StateHasChanged();

        try
        {
            var request = new DroneDataRequest
            {
                model = AnalysisType switch
                {
                    "single" => 2,
                    "all" => 3,
                    "subtasks" => 4,
                    "performance" => 5,
                    _ => 3
                },
                taskId = Guid.Parse(SelectedTaskId)
            };

            if (AnalysisType == "single" && !string.IsNullOrEmpty(SelectedDroneId))
            {
                request.drone = Guid.Parse(SelectedDroneId);
            }

            await OnDataLoaded.InvokeAsync(request);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private Task ExportTaskReport()
    {
        // 实现任务报告导出功能
        return Task.CompletedTask;
    }

    private string GetStatusBadgeClass(string status)
    {
        return status?.ToLower() switch
        {
            "completed" => "success",
            "inprogress" => "warning", 
            "pending" => "info",
            "failed" => "danger",
            "cancelled" => "secondary",
            "waitingforactivation" => "primary",
            "running" => "warning",
            "rantocompletion" => "success",
            "faulted" => "danger",
            _ => "dark"  // 改为dark，确保文字是白色，背景是深色
        };
    }
} 