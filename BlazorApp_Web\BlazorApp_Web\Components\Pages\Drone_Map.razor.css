﻿/* 基础布局 */
.drone-map-container {
    display: flex;
    width: 100%;
    height: 100vh;
    background: #f0f0f0;
}

/* 地图区域 */
.map-viewport {
    flex: 0 0 80%;
    position: relative;
    background: white;
    border: 1px solid #ddd;
    overflow: hidden;
}

/* 无人机标记 */
.drone-marker {
    position: absolute;
    width: 20px;
    height: 20px;
    background: red;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

    .drone-marker:hover {
        width: 24px;
        height: 24px;
        background: darkred;
    }

.drone-label {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    font-size: 12px;
}

/* 控制面板 */
.control-panel {
    flex: 1;
    padding: 15px;
    background: #fff;
    border-left: 1px solid #ddd;
    display: flex;
    flex-direction: column;
}

.drone-selector {
    padding: 8px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 15px 0;
}

.control-btn {
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

    .control-btn:hover {
        background: #0069d9;
    }

.status-display {
    margin-top: auto;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .drone-map-container {
        flex-direction: column;
    }

    .map-viewport {
        flex: 0 0 70vh;
        width: 100%;
    }
}
