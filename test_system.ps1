# 系统测试脚本
# 用于测试整个无人机管理系统的通信流程

param(
    [string]$PythonHost = "*************",
    [int]$PythonPort = 5007,
    [string]$ApiBaseUrl = "https://localhost:7001",
    [string]$BlazorUrl = "https://localhost:7002"
)

Write-Host "=== 无人机管理系统测试脚本 ===" -ForegroundColor Green
Write-Host "Python后端: $PythonHost`:$PythonPort" -ForegroundColor Yellow
Write-Host "API服务: $ApiBaseUrl" -ForegroundColor Yellow
Write-Host "Blazor前端: $BlazorUrl" -ForegroundColor Yellow
Write-Host ""

# 测试结果
$TestResults = @()

function Test-PythonBackend {
    Write-Host "1. 测试Python后端连接..." -ForegroundColor Cyan
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.Connect($PythonHost, $PythonPort)
        
        if ($tcpClient.Connected) {
            Write-Host "   ✓ Python后端连接成功" -ForegroundColor Green
            $tcpClient.Close()
            return $true
        } else {
            Write-Host "   ✗ Python后端连接失败" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ✗ Python后端连接异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-ApiService {
    Write-Host "2. 测试API服务..." -ForegroundColor Cyan
    
    try {
        # 测试健康检查
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/SocketMonitor/health" -Method Get -TimeoutSec 10
        Write-Host "   ✓ API服务健康检查通过" -ForegroundColor Green
        
        # 测试Socket状态
        $socketStatus = Invoke-RestMethod -Uri "$ApiBaseUrl/api/SocketMonitor/status" -Method Get -TimeoutSec 10
        Write-Host "   Socket连接状态: $($socketStatus.ConnectionStatus)" -ForegroundColor Yellow
        Write-Host "   是否已连接: $($socketStatus.IsConnected)" -ForegroundColor Yellow
        
        # 测试统计信息
        $statistics = Invoke-RestMethod -Uri "$ApiBaseUrl/api/SocketMonitor/statistics" -Method Get -TimeoutSec 10
        Write-Host "   已发送消息: $($statistics.MessagesSent)" -ForegroundColor Yellow
        Write-Host "   已接收消息: $($statistics.MessagesReceived)" -ForegroundColor Yellow
        Write-Host "   连接尝试次数: $($statistics.ConnectionAttempts)" -ForegroundColor Yellow
        
        return $true
    } catch {
        Write-Host "   ✗ API服务测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-SocketCommunication {
    Write-Host "3. 测试Socket通信..." -ForegroundColor Cyan
    
    try {
        # 发送测试消息
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/SocketMonitor/test-message?messageType=node_info&content=" -Method Post -TimeoutSec 10
        
        if ($response.Success) {
            Write-Host "   ✓ 测试消息发送成功 (耗时: $($response.Duration)ms)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ✗ 测试消息发送失败: $($response.ErrorMessage)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ✗ Socket通信测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-DroneOperations {
    Write-Host "4. 测试无人机操作..." -ForegroundColor Cyan
    
    try {
        # 获取无人机列表
        $drones = Invoke-RestMethod -Uri "$ApiBaseUrl/api/drones" -Method Get -TimeoutSec 10
        Write-Host "   当前无人机数量: $($drones.Count)" -ForegroundColor Yellow
        
        if ($drones.Count -gt 0) {
            $testDrone = $drones[0]
            Write-Host "   测试无人机: $($testDrone.Name) (ID: $($testDrone.Id))" -ForegroundColor Yellow
            
            # 这里可以添加更多的无人机操作测试
            # 比如更新状态、分配任务等
            
            Write-Host "   ✓ 无人机操作测试通过" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ⚠ 没有可用的无人机进行测试" -ForegroundColor Yellow
            return $true
        }
    } catch {
        Write-Host "   ✗ 无人机操作测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-BlazorFrontend {
    Write-Host "5. 测试Blazor前端..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri $BlazorUrl -Method Get -TimeoutSec 10
        
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✓ Blazor前端访问成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ✗ Blazor前端访问失败: HTTP $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ✗ Blazor前端测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-TestSummary {
    Write-Host ""
    Write-Host "=== 测试结果汇总 ===" -ForegroundColor Green
    
    $passedTests = ($TestResults | Where-Object { $_ -eq $true }).Count
    $totalTests = $TestResults.Count
    
    Write-Host "通过测试: $passedTests/$totalTests" -ForegroundColor Yellow
    
    if ($passedTests -eq $totalTests) {
        Write-Host "🎉 所有测试通过！系统运行正常。" -ForegroundColor Green
    } else {
        Write-Host "⚠️  部分测试失败，请检查系统配置。" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "建议的下一步操作:" -ForegroundColor Cyan
    Write-Host "1. 启动Python测试服务器: python test_socket_communication.py" -ForegroundColor White
    Write-Host "2. 在浏览器中访问: $BlazorUrl" -ForegroundColor White
    Write-Host "3. 测试无人机停用功能" -ForegroundColor White
    Write-Host "4. 查看Socket监控面板" -ForegroundColor White
}

# 执行测试
Write-Host "开始执行系统测试..." -ForegroundColor Green
Write-Host ""

$TestResults += Test-PythonBackend
$TestResults += Test-ApiService
$TestResults += Test-SocketCommunication
$TestResults += Test-DroneOperations
$TestResults += Test-BlazorFrontend

Show-TestSummary

# 如果需要，可以在这里添加更多的测试或诊断信息
Write-Host ""
Write-Host "测试完成。按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
