using WebApplication_Drone.Services.Interfaces;
using System.Collections.Concurrent;
using System.Text.Json;

namespace WebApplication_Drone.Services
{
    /// <summary>
    /// 内存缓存服务实现
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new();
        private readonly ILogger<CacheService> _logger;
        private long _hitCount = 0;
        private long _missCount = 0;
        private readonly DateTime _startTime = DateTime.UtcNow;

        public CacheService(ILogger<CacheService> logger)
        {
            _logger = logger;
        }

        #region 基础缓存操作

        public async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                var item = new CacheItem
                {
                    Value = value,
                    ExpiryTime = expiry.HasValue ? DateTime.UtcNow.Add(expiry.Value) : null,
                    CreatedTime = DateTime.UtcNow
                };
                _cache.AddOrUpdate(key, item, (k, v) => item);
                _logger.LogDebug("设置缓存: {Key}", key);
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置缓存失败: {Key}", key);
                return await Task.FromResult(false);
            }
        }

        public async Task<T?> GetAsync<T>(string key)
        {
            try
            {
                if (_cache.TryGetValue(key, out var item))
                {
                    if (item.ExpiryTime.HasValue && DateTime.UtcNow > item.ExpiryTime.Value)
                    {
                        _cache.TryRemove(key, out _);
                        _logger.LogDebug("缓存已过期: {Key}", key);
                        Interlocked.Increment(ref _missCount);
                        return await Task.FromResult<T?>(default);
                    }

                    _logger.LogDebug("命中缓存: {Key}", key);
                    Interlocked.Increment(ref _hitCount);
                    return await Task.FromResult((T?)item.Value);
                }

                _logger.LogDebug("缓存未命中: {Key}", key);
                Interlocked.Increment(ref _missCount);
                return await Task.FromResult<T?>(default);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存失败: {Key}", key);
                Interlocked.Increment(ref _missCount);
                return await Task.FromResult<T?>(default);
            }
        }

        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                var removed = _cache.TryRemove(key, out _);
                _logger.LogDebug("删除缓存: {Key}, 结果: {Removed}", key, removed);
                return await Task.FromResult(removed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除缓存失败: {Key}", key);
                return await Task.FromResult(false);
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                var exists = _cache.TryGetValue(key, out var item);
                if (exists && item != null && item.ExpiryTime.HasValue && DateTime.UtcNow > item.ExpiryTime.Value)
                {
                    _cache.TryRemove(key, out _);
                    exists = false;
                }
                return await Task.FromResult(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查缓存存在失败: {Key}", key);
                return await Task.FromResult(false);
            }
        }

        public async Task<bool> SetExpiryAsync(string key, TimeSpan expiry)
        {
            try
            {
                if (_cache.TryGetValue(key, out var item))
                {
                    item.ExpiryTime = DateTime.UtcNow.Add(expiry);
                    return await Task.FromResult(true);
                }
                return await Task.FromResult(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置缓存过期时间失败: {Key}", key);
                return await Task.FromResult(false);
            }
        }

        #endregion

        #region 批量操作

        public async Task<bool> SetBatchAsync<T>(Dictionary<string, T> items, TimeSpan? expiry = null)
        {
            try
            {
                foreach (var kvp in items)
                {
                    await SetAsync(kvp.Key, kvp.Value, expiry);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量设置缓存失败");
                return false;
            }
        }

        public async Task<Dictionary<string, T?>> GetBatchAsync<T>(IEnumerable<string> keys)
        {
            try
            {
                var result = new Dictionary<string, T?>();
                foreach (var key in keys)
                {
                    result[key] = await GetAsync<T>(key);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取缓存失败");
                return new Dictionary<string, T?>();
            }
        }

        public async Task<bool> RemoveBatchAsync(IEnumerable<string> keys)
        {
            try
            {
                foreach (var key in keys)
                {
                    await RemoveAsync(key);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除缓存失败");
                return false;
            }
        }

        #endregion

        #region 模式操作

        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            try
            {
                var keysToRemove = _cache.Keys.Where(key => IsMatch(key, pattern)).ToList();
                foreach (var key in keysToRemove)
                {
                    _cache.TryRemove(key, out _);
                }
                return await Task.FromResult(keysToRemove.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按模式删除缓存失败: {Pattern}", pattern);
                return await Task.FromResult(0L);
            }
        }

        public async Task<IEnumerable<string>> GetKeysByPatternAsync(string pattern)
        {
            try
            {
                var keys = _cache.Keys.Where(key => IsMatch(key, pattern)).ToList();
                return await Task.FromResult(keys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按模式获取键失败: {Pattern}", pattern);
                return await Task.FromResult(Enumerable.Empty<string>());
            }
        }

        public async Task<CacheInfo?> GetCacheInfoAsync(string key)
        {
            try
            {
                if (_cache.TryGetValue(key, out var item))
                {
                    return await Task.FromResult(new CacheInfo
                    {
                        Key = key,
                        ExpiryTime = item.ExpiryTime,
                        TimeToLive = item.ExpiryTime?.Subtract(DateTime.UtcNow),
                        Exists = true,
                        Type = item.Value?.GetType().Name ?? "null",
                        Size = EstimateSize(item.Value)
                    });
                }
                return await Task.FromResult<CacheInfo?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存信息失败: {Key}", key);
                return await Task.FromResult<CacheInfo?>(null);
            }
        }

        #endregion

        #region 统计和监控

        public async Task<CacheStatistics> GetStatisticsAsync()
        {
            try
            {
                var stats = new CacheStatistics
                {
                    TotalKeys = _cache.Count,
                    HitCount = _hitCount,
                    MissCount = _missCount,
                    HitRate = _hitCount + _missCount > 0 ? (double)_hitCount / (_hitCount + _missCount) : 0,
                    Uptime = DateTime.UtcNow - _startTime,
                    TotalMemoryUsage = EstimateTotalSize(),
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["ExpiredKeys"] = GetExpiredKeyCount(),
                        ["ServiceType"] = "MemoryCache"
                    }
                };
                return await Task.FromResult(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存统计失败");
                return await Task.FromResult(new CacheStatistics());
            }
        }

        public async Task<bool> FlushAllAsync()
        {
            try
            {
                _cache.Clear();
                _logger.LogInformation("已清空所有缓存");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空缓存失败");
                return await Task.FromResult(false);
            }
        }

        public async Task<long> GetCacheSizeAsync()
        {
            try
            {
                return await Task.FromResult(_cache.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存大小失败");
                return await Task.FromResult(0L);
            }
        }

        public async Task<bool> IsHealthyAsync()
        {
            try
            {
                // 简单的健康检查：尝试设置和获取一个测试值
                var testKey = $"health_check_{Guid.NewGuid()}";
                var testValue = "test";
                
                await SetAsync(testKey, testValue, TimeSpan.FromSeconds(1));
                var retrievedValue = await GetAsync<string>(testKey);
                await RemoveAsync(testKey);
                
                return testValue == retrievedValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "缓存健康检查失败");
                return false;
            }
        }

        #endregion

        #region 私有方法

        private bool IsMatch(string key, string pattern)
        {
            // 简单的通配符匹配实现
            if (pattern.Contains('*'))
            {
                var regexPattern = pattern.Replace("*", ".*");
                return System.Text.RegularExpressions.Regex.IsMatch(key, regexPattern);
            }
            return key.Contains(pattern);
        }

        private long EstimateSize(object? value)
        {
            if (value == null) return 0;
            
            try
            {
                var json = JsonSerializer.Serialize(value);
                return System.Text.Encoding.UTF8.GetByteCount(json);
            }
            catch
            {
                return value.ToString()?.Length ?? 0;
            }
        }

        private long EstimateTotalSize()
        {
            return _cache.Values.Sum(item => EstimateSize(item.Value));
        }

        private int GetExpiredKeyCount()
        {
            var now = DateTime.UtcNow;
            return _cache.Values.Count(item => item.ExpiryTime.HasValue && now > item.ExpiryTime.Value);
        }

        #endregion
    }

    /// <summary>
    /// 缓存项
    /// </summary>
    internal class CacheItem
    {
        public object? Value { get; set; }
        public DateTime? ExpiryTime { get; set; }
        public DateTime CreatedTime { get; set; }
    }
} 