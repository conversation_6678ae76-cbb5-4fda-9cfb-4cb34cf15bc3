/* 应用程序布局 */
.app-layout {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.app-layout-body {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 顶部导航栏 */
.app-header {
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.app-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 1.1rem;
}

.app-logo i {
    font-size: 24px;
    color: #ffd700;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    padding: 0 20px;
}

.breadcrumb-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    line-height: 1;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.user-menu:hover {
    background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 500;
}

/* 侧边栏 */
.app-sidebar {
    width: 280px;
    min-width: 200px;
    max-width: 500px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 999;
    position: relative;
    flex-shrink: 0;
    resize: horizontal;
    overflow-x: hidden;
}

.app-sidebar.collapsed {
    width: 0 !important;
    min-width: 0 !important;
    overflow: hidden;
    border-right: none;
}

.app-sidebar.collapsed .sidebar-resize-handle {
    display: none;
}

/* 拖拽手柄 */
.sidebar-resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    cursor: ew-resize;
    z-index: 1001;
}

.sidebar-resize-handle:hover {
    background: #667eea;
}

.sidebar-resize-handle.dragging {
    background: #667eea;
}

/* 主内容区域 */
.app-main {
    flex: 1;
    overflow: hidden;
    transition: all 0.3s ease;
}

.app-main.sidebar-collapsed {
    margin-left: 0;
}

.main-content {
    height: 100%;
    overflow-y: auto;
    padding: 24px;
}

/* 错误提示样式 */
#blazor-error-ui {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    bottom: 0;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 20px;
    position: fixed;
    width: 100%;
    z-index: 10000;
    color: white;
}

.error-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.error-content i {
    font-size: 24px;
    margin-right: 12px;
}

.error-actions {
    display: flex;
    gap: 16px;
}

.reload-btn, .dismiss-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reload-btn:hover, .dismiss-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-header {
        height: 56px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .app-title {
        display: none;
    }
    
    .header-center {
        display: none;
    }
    
    .user-name {
        display: none;
    }
    
    .app-sidebar {
        top: 56px;
        height: calc(100vh - 56px);
        transform: translateX(-100%);
    }
    
    .app-sidebar:not(.collapsed) {
        transform: translateX(0);
    }
    
    .app-main {
        margin-left: 0;
        margin-top: 56px;
    }
    
    .app-main.sidebar-collapsed {
        margin-left: 0;
    }
    
    .main-content {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .header-actions {
        gap: 8px;
    }
    
    .action-btn {
        padding: 6px;
        font-size: 16px;
    }
    
    .user-menu {
        padding: 6px 8px;
    }
    
    .user-avatar {
        width: 28px;
        height: 28px;
    }
}

/* 滚动条样式 */
.app-sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
    width: 6px;
}

.app-sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.app-sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.app-sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
