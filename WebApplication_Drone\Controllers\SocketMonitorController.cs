using Microsoft.AspNetCore.Mvc;
using WebApplication_Drone.Services;

namespace WebApplication_Drone.Controllers
{
    /// <summary>
    /// Socket连接监控控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class SocketMonitorController : ControllerBase
    {
        private readonly EnhancedSocketService _socketService;
        private readonly ILogger<SocketMonitorController> _logger;

        public SocketMonitorController(
            EnhancedSocketService socketService,
            ILogger<SocketMonitorController> logger)
        {
            _socketService = socketService;
            _logger = logger;
        }

        /// <summary>
        /// 获取Socket连接状态
        /// </summary>
        /// <returns>连接状态信息</returns>
        [HttpGet("status")]
        public ActionResult<object> GetStatus()
        {
            try
            {
                var status = new
                {
                    IsConnected = _socketService.IsConnected,
                    ConnectionStatus = _socketService.ConnectionStatus.ToString(),
                    Timestamp = DateTime.UtcNow
                };

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Socket状态失败: {Message}", ex.Message);
                return StatusCode(500, new { error = "获取状态失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取Socket统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public ActionResult<object> GetStatistics()
        {
            try
            {
                var statistics = _socketService.GetStatistics();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Socket统计信息失败: {Message}", ex.Message);
                return StatusCode(500, new { error = "获取统计信息失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 发送测试消息
        /// </summary>
        /// <param name="messageType">消息类型</param>
        /// <param name="content">消息内容</param>
        /// <returns>发送结果</returns>
        [HttpPost("test-message")]
        public async Task<ActionResult<object>> SendTestMessage(
            [FromQuery] string messageType = "node_info",
            [FromQuery] string content = "")
        {
            try
            {
                var message = new ClassLibrary_Core.Message.Message_Send
                {
                    type = messageType,
                    content = content
                };

                var result = await _socketService.SendMessageAsync(message);

                return Ok(new
                {
                    Success = result.Success,
                    ErrorMessage = result.ErrorMessage,
                    Duration = result.Duration.TotalMilliseconds,
                    Timestamp = result.Timestamp
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送测试消息失败: {Message}", ex.Message);
                return StatusCode(500, new { error = "发送测试消息失败", message = ex.Message });
            }
        }

        /// <summary>
        /// 获取详细的系统健康状态
        /// </summary>
        /// <returns>系统健康状态</returns>
        [HttpGet("health")]
        public ActionResult<object> GetHealth()
        {
            try
            {
                var health = new
                {
                    Socket = new
                    {
                        IsConnected = _socketService.IsConnected,
                        Status = _socketService.ConnectionStatus.ToString()
                    },
                    System = new
                    {
                        Timestamp = DateTime.UtcNow,
                        MachineName = Environment.MachineName,
                        ProcessorCount = Environment.ProcessorCount,
                        WorkingSet = Environment.WorkingSet,
                        GCMemory = GC.GetTotalMemory(false)
                    },
                    Application = new
                    {
                        Version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
                        Uptime = DateTime.UtcNow - System.Diagnostics.Process.GetCurrentProcess().StartTime.ToUniversalTime()
                    }
                };

                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取健康状态失败: {Message}", ex.Message);
                return StatusCode(500, new { error = "获取健康状态失败", message = ex.Message });
            }
        }
    }
}
