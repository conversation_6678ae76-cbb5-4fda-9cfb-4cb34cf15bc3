﻿@page "/drone_map"
@rendermode InteractiveServer
@using ClassLibrary_Core.Drone
@using ClassLibrary_Core.Common
@using Microsoft.AspNetCore.SignalR.Client
@using Microsoft.Extensions.Logging
@inject NavigationManager Navigation
@inject ILogger<Drone_Map> Logger
@inject IJSRuntime JSRuntime

<PageTitle>分散资源感知</PageTitle>

<style>
    .drone-group {
        transition: transform 2s ease-in-out;
    }
    
    .drone-icon {
        transition: all 1.5s ease-in-out;
    }

    .connection-path {
        transition: all 2s ease-in-out;
        fill: none;
        stroke: orange;
        stroke-width: 1.5;
    }

    .drone-label {
        transition: all 1.5s ease-in-out;
        font-size: 10px;
    }

    .info-box {
        transition: opacity 0.3s ease;
    }

    .drone-group:hover .drone-icon {
        filter: brightness(1.2);
    }

    .drone-group:hover .drone-label {
        font-weight: bold;
    }

    .selected-drone {
        filter: drop-shadow(0 0 4px #4CAF50);
    }
</style>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-3">
        <div class="col-12">
            <h3 class="mb-0">分散资源感知</h3>
            <p class="text-muted">实时监控和分析分散资源网络拓扑、节点状态和连接关系</p>
        </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="row">
        <div class="col-12">
@if (drones == null)
{
    <p>正在加载任务数据...</p>
}
else
{
<div class="drone-map-container">
    <div class="map-viewport"
         style="width:1200px;height:800px;border:1px solid #ccc;position:relative;">
        <svg width="1200" height="800">
            @foreach (var drone in drones.Where(d => !showOnlyConnected || visibleDroneIds.Contains(d.Id)))
            {
                var current = drone;
                @foreach (var adjId in current.ConnectedDroneIds)
                {
                    var adjacent = drones.FirstOrDefault(d => d.Id == adjId);
                    if (adjacent?.CurrentPosition != null && 
                        current.Status != DroneStatus.Offline && 
                        adjacent.Status != DroneStatus.Offline &&
                        (!showOnlyConnected || visibleDroneIds.Contains(adjId)))
                    {
                        var pathId = $"path_{current.Id}_{adjacent.Id}";
                        var x1 = current.CurrentPosition.Latitude_x;
                        var y1 = current.CurrentPosition.Longitude_y;
                        var x2 = adjacent.CurrentPosition.Latitude_x;
                        var y2 = adjacent.CurrentPosition.Longitude_y;
                        var pathData = $"M {x1} {y1} L {x2} {y2}";
                        
                        <path id="@pathId"
                              class="connection-path"
                              d="@pathData" />
                    }
                }
            }
        
            @foreach (var drone in drones.Where(d => !showOnlyConnected || visibleDroneIds.Contains(d.Id)))
            {
                if (drone.CurrentPosition != null)
                {
                    var img = GetDroneImage(drone.ModelStatus);
                    var color = GetStatusColor(drone.Status);
                    var isSelected = drone.Id == SelectedDrone?.Id;
                    <g class="drone-group @(isSelected ? "selected-drone" : "")" 
                       @onmouseover="() => ShowDroneDetails(drone)" 
                       @onmouseout="HideDroneDetails"
                       @onclick="() => SelectDrone(drone.Id.ToString())"
                       style="cursor: pointer;"
                       transform="translate(@(drone.CurrentPosition.Latitude_x - 16),@(drone.CurrentPosition.Longitude_y - 16))">
                        <!-- 无人机图标 -->
                        <image class="drone-icon"
                               href="@img"
                               width="32" height="32"
                               style="stroke:@color;stroke-width:@(isSelected ? "4" : "2");
                                      opacity:@(isSelected ? "1" : "0.8")" />
                        <!-- 状态圆圈 -->
                        <circle class="drone-icon"
                                cx="16" cy="16"
                                r="18"
                                fill="none"
                                stroke="@color"
                                stroke-width="3" />
                    </g>

                    @if (hoveredDrone == drone)
                    {
                        <g class="info-box" 
                           transform="translate(@(drone.CurrentPosition.Latitude_x + 25),@(drone.CurrentPosition.Longitude_y - 40))">
                            <!-- 详细信息背景 -->
                            <rect x="0" y="0"
                                  width="160" height="80"
                                  fill="white"
                                  stroke="gray"
                                  rx="5" ry="5"
                                  opacity="0.9"/>
                            <!-- 详细信息文本 -->
                            <text x="10" y="20" font-size="12">名称: @drone.Name</text>
                            <text x="10" y="40" font-size="12">状态: @drone.Status</text>
                            <text x="10" y="60" font-size="12">CPU: @drone.cpu_used_rate%</text>
                            <text x="10" y="75" font-size="12">内存: @drone.memory MB</text>
                        </g>
                    }
                }
            }
        </svg>
    </div>
    <!-- 控制面板 (20%宽度) -->
    <div class="control-panel">
                        <h3>分散资源监控</h3>    
        <select class="drone-selector" @bind="selectedDroneId" id="droneSelector">
            <option value="">-- 选择分散资源节点 --</option>
            @foreach(var drone in drones.OrderBy(d => ParseIPAndPort(d.Name)))          
            {
               <option value="@drone.Id">@drone.Name </option>
            }
        </select>
    
         <div class="button-group">
            <button class="control-btn" @onclick="StopDrone">停止</button>
            <button class="control-btn" @onclick="ToggleShowConnected">
                @(showOnlyConnected ? "显示全部" : "仅显示连接")
            </button>
        </div>

        <div class="status-display">
            <h4>状态信息</h4>
            <p>当前选择: @(SelectedDrone?.Name ?? "无")</p>
            <p>X坐标: @(SelectedDrone?.CurrentPosition.Latitude_x ?? 0)</p>
            <p>Y坐标: @(SelectedDrone?.CurrentPosition.Longitude_y ?? 0)</p>
        </div>
     </div>
</div>
}
        </div>
    </div>
</div>

@code {
    private HubConnection? hubConnection;
    private List<Drone> drones = new();
    private Dictionary<Guid, GPSPosition> lastPositions = new();
    private string? selectedDroneId;
    private Drone? SelectedDrone => !string.IsNullOrEmpty(selectedDroneId) && Guid.TryParse(selectedDroneId, out var id)
        ? drones.FirstOrDefault(d => d.Id == id)
        : null;
    private bool showOnlyConnected = false;
    private HashSet<Guid> visibleDroneIds = new();
    private Drone? hoveredDrone;

    private void ToggleShowConnected()
    {
        showOnlyConnected = !showOnlyConnected; 
        UpdateVisibleDrones();
    }

    private void UpdateVisibleDrones()
    {
        visibleDroneIds.Clear();

        if (showOnlyConnected && SelectedDrone != null)
        {
            // 添加选中无人机
            visibleDroneIds.Add(SelectedDrone.Id);

            // 添加直接连接的无人机
            foreach (var adjId in SelectedDrone.ConnectedDroneIds)
            {
                visibleDroneIds.Add(adjId);

                // 获取下一层邻接无人机
                var adjacentDrone = drones.FirstOrDefault(d => d.Id == adjId);
                if (adjacentDrone != null)
                {
                    foreach (var secondLevelId in adjacentDrone.ConnectedDroneIds)
                    {
                        visibleDroneIds.Add(secondLevelId);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 实时获取无人机信息
    /// </summary>
    /// <returns></returns>
    protected override async Task OnInitializedAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(Navigation.ToAbsoluteUri("/dronehub"))
            .Build();

        hubConnection.On<List<Drone>>("ReceiveDronesPosition", ds =>
        {
            if (ds != null)
            {
                foreach (var drone in ds)
                {
                    // 如果无人机是离线状态，保持其最后已知位置
                    if (drone.Status == DroneStatus.Offline && lastPositions.ContainsKey(drone.Id))
                    {
                        drone.CurrentPosition = lastPositions[drone.Id];
                    }
                    else if (drone.CurrentPosition != null)
                    {
                        // 更新最后已知位置
                        lastPositions[drone.Id] = drone.CurrentPosition;
                    }
                }
                drones = ds.Distinct().ToList();
                UpdateVisibleDrones();
                InvokeAsync(StateHasChanged);
            }
        });
        await hubConnection.StartAsync();
    }

    /// <summary>
    /// 检查是否需要更新无人机数据
    /// </summary>
    /// <param name="newDrones"></param>
    /// <returns></returns>
    private bool ShouldUpdateDrones(List<Drone>? newDrones)
    {
        if (newDrones == null || !newDrones.Any()) return false;
        
        // 如果数量不同，需要更新
        if (drones.Count != newDrones.Count) return true;
        
        // 检查是否有数据变化
        var existingDroneIds = drones.Select(d => d.Id).ToHashSet();
        var newDroneIds = newDrones.Select(d => d.Id).ToHashSet();
        
        // 如果ID集合不同，需要更新
        if (!existingDroneIds.SetEquals(newDroneIds)) return true;
        
        // 检查位置或状态是否有变化
        foreach (var newDrone in newDrones)
        {
            var existingDrone = drones.FirstOrDefault(d => d.Id == newDrone.Id);
            if (existingDrone != null)
            {
                if (existingDrone.Status != newDrone.Status ||
                    (existingDrone.Status != DroneStatus.Offline && // 如果不是离线状态，才检查位置变化
                    (existingDrone.CurrentPosition?.Latitude_x != newDrone.CurrentPosition?.Latitude_x ||
                    existingDrone.CurrentPosition?.Longitude_y != newDrone.CurrentPosition?.Longitude_y)))
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    /// <summary>
    /// 状态颜色映射
    /// </summary>
    /// <param name="status"></param>
    /// <returns></returns>
    private string GetStatusColor(DroneStatus status) => status switch
    {
        DroneStatus.Idle => "gray",
        DroneStatus.InMission => "green",
        DroneStatus.Returning => "blue",
        DroneStatus.Maintenance => "orange",
        DroneStatus.Offline => "yellow",
        DroneStatus.Emergency => "red",
        _ => "gray"
    };
    /// <summary>
    /// 类型图片映射
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private string GetDroneImage(ModelStatus? type) => type switch
    {
        ModelStatus.True => "images/PhysicalDrone.png",
        ModelStatus.Vm => "images/virtualdrone.png",
    };

    public bool IsConnected =>
        hubConnection?.State == HubConnectionState.Connected;

    /// <summary>
    /// 停用无人机（从集群中移除）
    /// </summary>
    public void StopDrone()
    {
        Logger.LogInformation("StopDrone 方法被调用。");
        if (hubConnection is null)
        {
            Logger.LogInformation("HubConnection is null.");
            return;
        }
        Logger.LogInformation("当前的 HubConnection 状态: {State}", hubConnection.State);

        if (SelectedDrone != null && hubConnection.State == HubConnectionState.Connected)
        {
            Logger.LogInformation("正在为无人机 {DroneId} 调用 SetDroneOffline（停用）。", SelectedDrone.Id);
            hubConnection.InvokeAsync("SetDroneOffline", SelectedDrone.Id);
        }
        else
        {
            if (SelectedDrone == null)
            {
                Logger.LogInformation("未选择任何无人机。");
            }
            if (hubConnection.State != HubConnectionState.Connected)
            {
                Logger.LogInformation("HubConnection 未连接。");
            }
        }
    }

    /// <summary>
    /// 结束连接
    /// </summary>
    /// <returns></returns>
    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    private void ShowDroneDetails(Drone drone)
    {
        hoveredDrone = drone;
        StateHasChanged();
    }

    private void HideDroneDetails()
    {
        hoveredDrone = null;
        StateHasChanged();
    }

    private (string ip, int port) ParseIPAndPort(string name)
    {
        try
        {
            var parts = name.Split(':');
            if (parts.Length == 2 && int.TryParse(parts[1], out int port))
            {
                return (parts[0], port);
            }
        }
        catch { }
        return (name, 0);
    }

    private void SelectDrone(string droneId)
    {
        selectedDroneId = droneId;
        StateHasChanged();
        
        // 使用 JS 交互来滚动下拉列表到选中项
        var js = "setTimeout(() => { const select = document.getElementById('droneSelector'); " +
                "const option = Array.from(select.options).find(o => o.value === '" + droneId + "'); " +
                "if (option) { option.scrollIntoView({ behavior: 'smooth', block: 'center' }); } }, 0);";
        JSRuntime.InvokeVoidAsync("eval", js);
    }
}

