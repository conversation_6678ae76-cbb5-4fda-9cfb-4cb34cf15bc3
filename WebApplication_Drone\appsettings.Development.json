{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "WebApplication_Drone.Services.EnhancedSocketService": "Debug", "WebApplication_Drone.Services.SocketService": "Debug", "WebApplication_Drone.Controllers.SocketMonitorController": "Debug", "BlazorApp_Web.Hubs.DroneHub": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "}}, "SocketConfiguration": {"PythonHost": "*************", "PythonPort": 5007, "ConnectionTimeoutSeconds": 10, "MaxRetries": 3, "RetryIntervalMs": 2000, "AutoReconnect": true, "HeartbeatIntervalSeconds": 15, "MaxQueueSize": 500}}