/* 导航菜单容器 */
.nav-menu {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* 顶部品牌区域 */
.top-row {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    border: none;
}

.container-fluid {
    padding: 0;
}

.navbar-brand {
    color: white !important;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    display: block;
    line-height: 1.3;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    padding-left: 28px;
    position: relative;
}

.navbar-brand::before {
    content: "🤖";
    font-size: 20px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

/* 用户信息卡片 */
.user-card {
    padding: 20px 16px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 3px solid #667eea;
    object-fit: cover;
}

.user-details h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.user-role {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.user-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6c757d;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
}

.status-indicator.online {
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 8px rgba(40, 167, 69, 0.5); }
    50% { box-shadow: 0 0 16px rgba(40, 167, 69, 0.8); }
    100% { box-shadow: 0 0 8px rgba(40, 167, 69, 0.5); }
}

/* 导航内容区域 */
.nav-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
}

/* 导航分组 */
.nav-section {
    margin-bottom: 24px;
}

.section-title {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 16px 12px 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

/* 导航列表 */
.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0;
}

/* 导航链接 */
.nav-link {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    border-radius: 0;
    margin: 0 8px;
    border-radius: 8px;
    min-height: 48px;
}

.nav-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    transform: translateX(4px);
}

.nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: #ffd700;
    border-radius: 2px;
}

/* 导航图标 */
.nav-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
}

/* 导航文本 */
.nav-text {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    line-height: 1.2;
}

/* 导航徽章和计数 */
.nav-badge, .nav-count {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    min-width: 18px;
}

.nav-badge {
    background: #28a745;
    color: white;
}

.nav-count {
    background: #dc3545;
    color: white;
}

.nav-link:hover .nav-badge,
.nav-link:hover .nav-count,
.nav-link.active .nav-badge,
.nav-link.active .nav-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* 底部信息 */
.nav-footer {
    border-top: 1px solid #e9ecef;
    padding: 16px;
    background: #f8f9fa;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6c757d;
}

.info-item i {
    font-size: 14px;
    width: 16px;
}

.version-info {
    text-align: center;
    font-size: 11px;
    color: #adb5bd;
    font-weight: 500;
}

/* 移动端切换按钮 */
.navbar-toggler {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        position: relative;
    }
    
    .user-card {
        padding: 16px 12px;
    }
    
    .user-info {
        margin-bottom: 8px;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
    }
    
    .user-details h4 {
        font-size: 14px;
    }
    
    .nav-content {
        padding: 12px 0;
    }
    
    .nav-section {
        margin-bottom: 20px;
    }
    
    .section-title {
        margin: 0 12px 8px 12px;
    }
    
    .nav-link {
        padding: 10px 12px;
        margin: 0 6px;
    }
    
    .nav-footer {
        padding: 12px;
    }
    
    .system-info {
        flex-direction: row;
        justify-content: space-between;
    }
}

/* 侧边栏收缩状态 */
.app-sidebar.collapsed .nav-text,
.app-sidebar.collapsed .section-title,
.app-sidebar.collapsed .user-details,
.app-sidebar.collapsed .user-status,
.app-sidebar.collapsed .nav-badge,
.app-sidebar.collapsed .nav-count,
.app-sidebar.collapsed .nav-footer {
    display: none;
}

.app-sidebar.collapsed .user-card {
    padding: 16px 8px;
    text-align: center;
}

.app-sidebar.collapsed .user-info {
    justify-content: center;
    margin-bottom: 0;
}

.app-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 12px 8px;
    margin: 0 4px;
}

.app-sidebar.collapsed .nav-icon {
    margin-right: 0;
}

.app-sidebar.collapsed .nav-section {
    margin-bottom: 16px;
}

/* 响应式品牌标题 */
@media (max-width: 300px) {
    .navbar-brand {
        font-size: 12px;
        padding-left: 24px;
    }
    
    .navbar-brand::before {
        font-size: 16px;
    }
}

@media (min-width: 350px) {
    .navbar-brand {
        font-size: 14px;
    }
}

@media (min-width: 400px) {
    .navbar-brand {
        font-size: 16px;
    }
}

/* 滚动条样式 */
.nav-content::-webkit-scrollbar {
    width: 4px;
}

.nav-content::-webkit-scrollbar-track {
    background: transparent;
}

.nav-content::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 2px;
}

.nav-content::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}
