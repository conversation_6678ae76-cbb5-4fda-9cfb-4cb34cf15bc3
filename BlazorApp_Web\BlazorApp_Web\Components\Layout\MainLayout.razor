﻿@inherits LayoutComponentBase
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="app-layout">
    <!-- 顶部导航栏 -->
    <header class="app-header">
        <div class="header-content">
            <div class="header-left">
                <button class="sidebar-toggle" @onclick="ToggleSidebar" title="切换侧边栏">
                    <i class="bi bi-list"></i>
                </button>
                <div class="app-logo">
                    <i class="bi bi-robot"></i>
                    <span class="app-title">基于分散计算的动态负载均衡任务分配原型系统</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="breadcrumb-container">
                    <!-- 动态面包屑导航将在这里显示 -->
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" title="通知">
                        <i class="bi bi-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="action-btn" title="设置">
                        <i class="bi bi-gear"></i>
                    </button>
                    <div class="user-menu">
                        <img src="/images/user-avatar.png" alt="用户头像" class="user-avatar" />
                        <span class="user-name">管理员</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主体布局 -->
    <div class="app-layout-body">
        <!-- 侧边栏 -->
        <aside class="app-sidebar @(isSidebarCollapsed ? "collapsed" : "")" style="width: @(sidebarWidth)px;">
            <NavMenu />
            <!-- 拖拽手柄 -->
            <div class="sidebar-resize-handle" @onmousedown="StartResize" @onmousedown:preventDefault="true"></div>
        </aside>

        <!-- 主内容区域 -->
        <main class="app-main @(isSidebarCollapsed ? "sidebar-collapsed" : "")">
            <div class="main-content">
                @Body
            </div>
        </main>
    </div>
</div>

<!-- 错误提示 -->
<div id="blazor-error-ui">
    <div class="error-content">
        <i class="bi bi-exclamation-triangle"></i>
        <span>系统发生错误，请刷新页面重试</span>
        <div class="error-actions">
            <a href="" class="reload-btn">刷新页面</a>
            <a class="dismiss-btn" @onclick="DismissError">关闭</a>
        </div>
    </div>
</div>

@code {
    private bool isSidebarCollapsed = false;
    private int sidebarWidth = 280;
    private bool isResizing = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeSidebarResize();
        }
    }

    private void ToggleSidebar()
    {
        isSidebarCollapsed = !isSidebarCollapsed;
        StateHasChanged();
    }

    private async Task StartResize()
    {
        if (!isSidebarCollapsed)
        {
            isResizing = true;
            await JSRuntime.InvokeVoidAsync("startSidebarResize", DotNetObjectReference.Create(this));
        }
    }

    [JSInvokable]
    public void UpdateSidebarWidth(int width)
    {
        sidebarWidth = Math.Max(200, Math.Min(500, width));
        StateHasChanged();
    }

    [JSInvokable]
    public void StopResize()
    {
        isResizing = false;
    }

    private async Task InitializeSidebarResize()
    {
        await JSRuntime.InvokeVoidAsync("eval", @"
            window.startSidebarResize = function(dotnetRef) {
                let isResizing = false;
                let startX = 0;
                let startWidth = 0;

                function onMouseDown(e) {
                    if (e.target.classList.contains('sidebar-resize-handle')) {
                        isResizing = true;
                        startX = e.clientX;
                        startWidth = document.querySelector('.app-sidebar').offsetWidth;
                        document.body.style.cursor = 'ew-resize';
                        document.body.style.userSelect = 'none';
                        e.target.classList.add('dragging');
                        e.preventDefault();
                    }
                }

                function onMouseMove(e) {
                    if (isResizing) {
                        const deltaX = e.clientX - startX;
                        const newWidth = startWidth + deltaX;
                        dotnetRef.invokeMethodAsync('UpdateSidebarWidth', newWidth);
                    }
                }

                function onMouseUp(e) {
                    if (isResizing) {
                        isResizing = false;
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                        document.querySelector('.sidebar-resize-handle').classList.remove('dragging');
                        dotnetRef.invokeMethodAsync('StopResize');
                    }
                }

                document.addEventListener('mousedown', onMouseDown);
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            };
        ");
    }

    private async Task DismissError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("eval", @"
                const errorElement = document.getElementById('blazor-error-ui');
                if (errorElement) {
                    errorElement.style.display = 'none';
                }
            ");
        }
        catch (Exception)
        {
            // JavaScript调用失败时的备用方案
        }
    }
}
